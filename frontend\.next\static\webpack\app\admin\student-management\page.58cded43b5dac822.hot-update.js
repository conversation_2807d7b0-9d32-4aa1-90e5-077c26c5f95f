"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/student-management/page",{

/***/ "(app-pages-browser)/./src/app/admin/student-management/StudentProfile.jsx":
/*!*************************************************************!*\
  !*** ./src/app/admin/student-management/StudentProfile.jsx ***!
  \*************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StudentProfile)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Edit_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Edit,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Edit_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Edit,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Edit_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Edit,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Edit_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Edit,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_FaBuilding_FaFileAlt_FaMapMarkerAlt_FaPhoneAlt_FaSpinner_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=FaBuilding,FaFileAlt,FaMapMarkerAlt,FaPhoneAlt,FaSpinner,FaUser!=!react-icons/fa */ \"(app-pages-browser)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var _api_students__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../api/students */ \"(app-pages-browser)/./src/api/students.js\");\n/* harmony import */ var _DocumentsModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./DocumentsModal */ \"(app-pages-browser)/./src/app/admin/student-management/DocumentsModal.jsx\");\n/* harmony import */ var _ResumeModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ResumeModal */ \"(app-pages-browser)/./src/app/admin/student-management/ResumeModal.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction StudentProfile(param) {\n    let { selectedStudent, editedStudent, isEditing, handleBackToList, handleEdit, handleSave, handleCancel, handleInputChange, departmentOptions } = param;\n    _s();\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [semesterMarksheets, setSemesterMarksheets] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isDocumentsModalOpen, setIsDocumentsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isResumeModalOpen, setIsResumeModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [companyStats, setCompanyStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        loading: false,\n        totalListings: 0,\n        eligibleJobs: 0\n    });\n    const [resumeCount, setResumeCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [lastResumeUpdate, setLastResumeUpdate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Use selectedStudent as the profile data, but allow fetching more details if needed\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentProfile.useEffect\": ()=>{\n            if (selectedStudent) {\n                console.log('Selected Student Data:', selectedStudent);\n                console.log('Edited Student Data:', editedStudent);\n                setProfile(selectedStudent);\n                // Optionally fetch additional details if needed\n                fetchAdditionalDetails(selectedStudent.id);\n                // Fetch company statistics\n                fetchCompanyStats(selectedStudent.id);\n                // Fetch resume information\n                fetchResumeInfo(selectedStudent.id);\n            }\n        }\n    }[\"StudentProfile.useEffect\"], [\n        selectedStudent\n    ]);\n    // Fetch additional details if needed\n    const fetchAdditionalDetails = async (studentId)=>{\n        if (!studentId) return;\n        try {\n            setLoading(true);\n            // This could fetch semester marksheets or other detailed info not included in the list view\n            // const details = await studentsAPI.getStudentDetails(studentId);\n            // setSemesterMarksheets(details.semesterMarksheets || []);\n            setLoading(false);\n        } catch (err) {\n            console.error('Error fetching student details:', err);\n            setLoading(false);\n        }\n    };\n    // Fetch company statistics for the student\n    const fetchCompanyStats = async (studentId)=>{\n        if (!studentId) return;\n        try {\n            setCompanyStats((prev)=>({\n                    ...prev,\n                    loading: true\n                }));\n            // Mock implementation - replace with actual API call\n            // const stats = await studentsAPI.getStudentCompanyStats(studentId);\n            // For now, provide mock data\n            const mockStats = {\n                loading: false,\n                totalListings: 25,\n                eligibleJobs: 18\n            };\n            setCompanyStats(mockStats);\n        } catch (err) {\n            console.error('Error fetching company stats:', err);\n            setCompanyStats({\n                loading: false,\n                totalListings: 0,\n                eligibleJobs: 0\n            });\n        }\n    };\n    // Fetch resume information for the student\n    const fetchResumeInfo = async (studentId)=>{\n        if (!studentId) return;\n        try {\n            // Mock implementation - replace with actual API call\n            // const resumeInfo = await studentsAPI.getStudentResumes(studentId);\n            // For now, provide mock data\n            const mockResumeCount = 2;\n            const mockLastUpdate = new Date('2024-01-15').toISOString();\n            setResumeCount(mockResumeCount);\n            setLastResumeUpdate(mockLastUpdate);\n        } catch (err) {\n            console.error('Error fetching resume info:', err);\n            setResumeCount(0);\n            setLastResumeUpdate(null);\n        }\n    };\n    // Calculate overall CGPA\n    const calculateOverallCGPA = ()=>{\n        return (profile === null || profile === void 0 ? void 0 : profile.cgpa) || 'N/A';\n    };\n    // Calculate percentage from CGPA (approximation)\n    const calculatePercentage = (cgpa)=>{\n        if (!cgpa || cgpa === 'N/A') return '';\n        const numericCgpa = parseFloat(cgpa);\n        if (isNaN(numericCgpa)) return '';\n        return (numericCgpa * 9.5).toFixed(2) + '%';\n    };\n    // Format year range if available\n    const formatEducationPeriod = (joiningYear, passoutYear)=>{\n        if (joiningYear && passoutYear) {\n            return \"\".concat(joiningYear, \" - \").concat(passoutYear);\n        }\n        return 'N/A';\n    };\n    // Get year range for student\n    const getYearRange = ()=>{\n        if ((profile === null || profile === void 0 ? void 0 : profile.joining_year) && (profile === null || profile === void 0 ? void 0 : profile.passout_year)) {\n            return \"\".concat(profile.joining_year, \" - \").concat(profile.passout_year);\n        }\n        if ((editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.joining_year) && (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.passout_year)) {\n            return \"\".concat(editedStudent.joining_year, \" - \").concat(editedStudent.passout_year);\n        }\n        if ((profile === null || profile === void 0 ? void 0 : profile.year) || (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.year)) {\n            return (profile === null || profile === void 0 ? void 0 : profile.year) || (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.year);\n        }\n        return null;\n    };\n    // Get semester CGPA\n    const getSemesterCGPA = (semester)=>{\n        // First try to get from semesterMarksheets (if available)\n        if (semesterMarksheets && semesterMarksheets.length > 0) {\n            var _semesterMarksheets_;\n            return ((_semesterMarksheets_ = semesterMarksheets[semester - 1]) === null || _semesterMarksheets_ === void 0 ? void 0 : _semesterMarksheets_.cgpa) || '-';\n        }\n        // Then try to get from editedStudent semester_cgpas\n        if ((editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.semester_cgpas) && editedStudent.semester_cgpas.length > 0) {\n            const semesterData = editedStudent.semester_cgpas.find((s)=>s.semester === semester);\n            return (semesterData === null || semesterData === void 0 ? void 0 : semesterData.cgpa) || '-';\n        }\n        // Finally try to get from profile semester_cgpas\n        if ((profile === null || profile === void 0 ? void 0 : profile.semester_cgpas) && profile.semester_cgpas.length > 0) {\n            const semesterData = profile.semester_cgpas.find((s)=>s.semester === semester);\n            return (semesterData === null || semesterData === void 0 ? void 0 : semesterData.cgpa) || '-';\n        }\n        return '-';\n    };\n    // Handle semester CGPA change\n    const handleSemesterCGPAChange = (semester, value)=>{\n        const currentSemesterCGPAs = (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.semester_cgpas) || [];\n        const updatedSemesterCGPAs = [\n            ...currentSemesterCGPAs\n        ];\n        // Find existing semester data or create new one\n        const existingIndex = updatedSemesterCGPAs.findIndex((s)=>s.semester === semester);\n        if (existingIndex >= 0) {\n            // Update existing semester\n            updatedSemesterCGPAs[existingIndex] = {\n                ...updatedSemesterCGPAs[existingIndex],\n                cgpa: value\n            };\n        } else {\n            // Add new semester data\n            updatedSemesterCGPAs.push({\n                semester: semester,\n                cgpa: value\n            });\n        }\n        // Sort by semester number\n        updatedSemesterCGPAs.sort((a, b)=>a.semester - b.semester);\n        // Update the edited student\n        handleInputChange('semester_cgpas', updatedSemesterCGPAs);\n    };\n    // Handle resume upload\n    const handleResumeUpload = async (file)=>{\n        try {\n            await _api_students__WEBPACK_IMPORTED_MODULE_3__.studentsAPI.uploadResume(file);\n            // Refresh resume info after upload\n            if (selectedStudent) {\n                fetchResumeInfo(selectedStudent.id);\n            }\n        } catch (err) {\n            console.error('Error uploading resume:', err);\n            alert('Failed to upload resume. Please try again.');\n        }\n    };\n    // Handle resume delete\n    const handleResumeDelete = async (resumeId)=>{\n        try {\n            await _api_students__WEBPACK_IMPORTED_MODULE_3__.studentsAPI.deleteResume(resumeId);\n            // Refresh resume info after delete\n            if (selectedStudent) {\n                fetchResumeInfo(selectedStudent.id);\n            }\n        } catch (err) {\n            console.error('Error deleting resume:', err);\n            alert('Failed to delete resume. Please try again.');\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBuilding_FaFileAlt_FaMapMarkerAlt_FaPhoneAlt_FaSpinner_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_6__.FaSpinner, {\n                    className: \"animate-spin text-blue-500 text-xl mr-2\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                    lineNumber: 235,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: \"Loading details...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                    lineNumber: 236,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n            lineNumber: 234,\n            columnNumber: 7\n        }, this);\n    }\n    if (!selectedStudent && !editedStudent) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center p-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-500\",\n                    children: \"No student selected\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                    lineNumber: 244,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleBackToList,\n                    className: \"mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                    children: \"Back to List\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                    lineNumber: 245,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n            lineNumber: 243,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleBackToList,\n                        className: \"flex items-center gap-2 text-blue-600 hover:text-blue-800 transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Edit_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                lineNumber: 263,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Back to List\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                lineNumber: 264,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                        lineNumber: 259,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleSave,\n                                    className: \"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors mr-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Edit_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Save\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleCancel,\n                                    className: \"px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Edit_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleEdit,\n                            className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Edit_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Edit\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                lineNumber: 294,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                            lineNumber: 290,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                        lineNumber: 267,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                lineNumber: 258,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-xl shadow-sm overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-blue-50 p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-16 h-16 bg-blue-500 text-white flex items-center justify-center rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.name) ? editedStudent.name[0].toUpperCase() : 'S'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-grow\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-xl font-semibold text-gray-800\",\n                                            children: isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.name) || '',\n                                                onChange: (e)=>handleInputChange('name', e.target.value),\n                                                className: \"w-full p-1 border rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 19\n                                            }, this) : (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.name) || 'Unknown Student'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1 text-gray-600\",\n                                            children: [\n                                                isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.rollNumber) || '',\n                                                    onChange: (e)=>handleInputChange('rollNumber', e.target.value),\n                                                    className: \"p-1 border rounded mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 331,\n                                                    columnNumber: 19\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        \"ID: \",\n                                                        (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.rollNumber) || 'N/A'\n                                                    ]\n                                                }, void 0, true),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mx-2\",\n                                                    children: \"|\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 17\n                                                }, this),\n                                                isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.department) || '',\n                                                    onChange: (e)=>handleInputChange('department', e.target.value),\n                                                    className: \"p-1 border rounded mr-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"\",\n                                                            children: \"Select Department\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 349,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        departmentOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: option.value,\n                                                                children: option.label\n                                                            }, option.value, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                lineNumber: 351,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 19\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        \"Department: \",\n                                                        (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.department) || 'N/A'\n                                                    ]\n                                                }, void 0, true),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mx-2\",\n                                                    children: \"|\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 360,\n                                                    columnNumber: 17\n                                                }, this),\n                                                isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.year) || '',\n                                                    onChange: (e)=>handleInputChange('year', e.target.value),\n                                                    className: \"p-1 border rounded\",\n                                                    placeholder: \"YYYY-YYYY\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 363,\n                                                    columnNumber: 19\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        \"Year: \",\n                                                        getYearRange() || 'N/A'\n                                                    ]\n                                                }, void 0, true)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-100 px-3 py-1 rounded-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-blue-600 font-medium\",\n                                                children: [\n                                                    \"CGPA: \",\n                                                    calculateOverallCGPA()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 378,\n                                                columnNumber: 17\n                                            }, this),\n                                            calculateOverallCGPA() !== 'N/A' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500 ml-1\",\n                                                children: [\n                                                    \"(\",\n                                                    calculatePercentage(calculateOverallCGPA()),\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                    lineNumber: 376,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                            lineNumber: 307,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                        lineNumber: 306,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-medium mb-4 text-gray-800 border-b pb-2\",\n                                            children: \"Personal Information\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 392,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-gray-600 text-sm mb-1\",\n                                                            children: \"Email\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 396,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"email\",\n                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.email) || '',\n                                                            onChange: (e)=>handleInputChange('email', e.target.value),\n                                                            className: \"w-full p-2 border rounded-lg\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 398,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-800\",\n                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.email) || 'N/A'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 405,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 395,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-gray-600 text-sm mb-1\",\n                                                            children: \"Phone\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 410,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"tel\",\n                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.phone) || '',\n                                                            onChange: (e)=>handleInputChange('phone', e.target.value),\n                                                            className: \"w-full p-2 border rounded-lg\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 412,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-800\",\n                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.phone) || 'N/A'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 419,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 409,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-gray-600 text-sm mb-1\",\n                                                            children: \"Date of Birth\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 424,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"date\",\n                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.dateOfBirth) || '',\n                                                            onChange: (e)=>handleInputChange('dateOfBirth', e.target.value),\n                                                            className: \"w-full p-2 border rounded-lg\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 426,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-800\",\n                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.dateOfBirth) || 'N/A'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 433,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-gray-600 text-sm mb-1\",\n                                                            children: \"Parent Contact\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 438,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"tel\",\n                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.parentContact) || '',\n                                                            onChange: (e)=>handleInputChange('parentContact', e.target.value),\n                                                            className: \"w-full p-2 border rounded-lg\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 440,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-800\",\n                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.parentContact) || 'N/A'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 447,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 437,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-medium mb-4 text-gray-800 border-b pb-2\",\n                                            children: \"Address & Education\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 455,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-gray-600 text-sm mb-1\",\n                                                            children: \"Address\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 459,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.address) || '',\n                                                            onChange: (e)=>handleInputChange('address', e.target.value),\n                                                            className: \"w-full p-2 border rounded-lg\",\n                                                            rows: 2\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 461,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-800\",\n                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.address) || 'N/A'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 468,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-gray-600 text-sm mb-1\",\n                                                            children: \"Education\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 473,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.education) || '',\n                                                            onChange: (e)=>handleInputChange('education', e.target.value),\n                                                            className: \"w-full p-2 border rounded-lg\",\n                                                            rows: 2\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 475,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-800\",\n                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.education) || 'N/A'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 482,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 472,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 457,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                    lineNumber: 454,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"md:col-span-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-medium mb-4 text-gray-800 border-b pb-2\",\n                                            children: \"Skills\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 490,\n                                            columnNumber: 15\n                                        }, this),\n                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: Array.isArray(editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.skills) ? editedStudent.skills.join(', ') : (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.skills) || '',\n                                            onChange: (e)=>handleInputChange('skills', e.target.value.split(',').map((s)=>s.trim())),\n                                            className: \"w-full p-2 border rounded-lg\",\n                                            rows: 2,\n                                            placeholder: \"Enter skills separated by commas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 493,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: Array.isArray(editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.skills) && editedStudent.skills.length > 0 ? editedStudent.skills.map((skill, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm\",\n                                                    children: skill\n                                                }, index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 504,\n                                                    columnNumber: 23\n                                                }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"No skills listed\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 512,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 501,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                    lineNumber: 489,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                            lineNumber: 389,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                        lineNumber: 388,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-6 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg p-5 shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-6 text-gray-800\",\n                                        children: \"Academic\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 524,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-800\",\n                                                        children: \"Semester Wise score\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 527,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-blue-50 inline-flex items-center px-3 py-1 rounded-full ml-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-blue-600 font-medium\",\n                                                                children: calculateOverallCGPA()\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                lineNumber: 529,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-500 ml-1\",\n                                                                children: \"CGPA\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                lineNumber: 530,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-blue-600 ml-2\",\n                                                                children: calculatePercentage(calculateOverallCGPA())\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                lineNumber: 531,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 528,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 526,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-lg font-semibold text-gray-800\",\n                                                children: isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.joining_year) || '',\n                                                            onChange: (e)=>handleInputChange('joining_year', e.target.value),\n                                                            className: \"w-20 p-1 border rounded text-sm\",\n                                                            placeholder: \"2020\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 537,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"-\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 544,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.passout_year) || '',\n                                                            onChange: (e)=>handleInputChange('passout_year', e.target.value),\n                                                            className: \"w-20 p-1 border rounded text-sm\",\n                                                            placeholder: \"2024\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 545,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 536,\n                                                    columnNumber: 21\n                                                }, this) : formatEducationPeriod(editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.joining_year, editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.passout_year)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 534,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 525,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"overflow-x-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                className: \"w-full border-collapse border border-gray-300\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            className: \"bg-gray-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"border border-gray-300 px-4 py-3 text-sm font-semibold text-gray-700\",\n                                                                    children: \"Sem\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                    lineNumber: 564,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                [\n                                                                    1,\n                                                                    2,\n                                                                    3,\n                                                                    4,\n                                                                    5,\n                                                                    6,\n                                                                    7,\n                                                                    8\n                                                                ].map((sem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"border border-gray-300 px-4 py-3 text-sm font-semibold text-gray-700\",\n                                                                        children: sem\n                                                                    }, sem, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                        lineNumber: 566,\n                                                                        columnNumber: 25\n                                                                    }, this))\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 563,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 562,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"border border-gray-300 px-4 py-3 text-sm font-medium text-gray-700\",\n                                                                    children: \"Cgpa\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                    lineNumber: 572,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                [\n                                                                    1,\n                                                                    2,\n                                                                    3,\n                                                                    4,\n                                                                    5,\n                                                                    6,\n                                                                    7,\n                                                                    8\n                                                                ].map((sem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"border border-gray-300 px-4 py-3 text-sm text-gray-700\",\n                                                                        children: isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: getSemesterCGPA(sem) !== '-' ? getSemesterCGPA(sem) : '',\n                                                                            onChange: (e)=>handleSemesterCGPAChange(sem, e.target.value),\n                                                                            className: \"w-full p-1 border rounded text-sm text-center\",\n                                                                            placeholder: \"0.0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 576,\n                                                                            columnNumber: 29\n                                                                        }, this) : getSemesterCGPA(sem)\n                                                                    }, sem, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                        lineNumber: 574,\n                                                                        columnNumber: 25\n                                                                    }, this))\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 571,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 570,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 561,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 560,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 559,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                        className: \"my-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 593,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-gray-800\",\n                                                                children: \"Class XII\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                lineNumber: 599,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-blue-50 inline-flex items-center px-3 py-1 rounded-full ml-3\",\n                                                                children: isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.twelfth_cgpa) || '',\n                                                                            onChange: (e)=>handleInputChange('twelfth_cgpa', e.target.value),\n                                                                            className: \"w-16 p-1 border rounded text-sm text-blue-600 bg-transparent\",\n                                                                            placeholder: \"9.5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 603,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-500 ml-1\",\n                                                                            children: \"CGPA\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 610,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.twelfth_percentage) || '',\n                                                                            onChange: (e)=>handleInputChange('twelfth_percentage', e.target.value),\n                                                                            className: \"w-16 p-1 border rounded text-sm text-blue-600 bg-transparent ml-2\",\n                                                                            placeholder: \"95%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 611,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-blue-600 font-medium\",\n                                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.twelfth_cgpa) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 621,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-500 ml-1\",\n                                                                            children: \"CGPA\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 622,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-blue-600 ml-2\",\n                                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.twelfth_percentage) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 623,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                lineNumber: 600,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 598,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-lg font-semibold text-gray-800\",\n                                                        children: isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.twelfth_year_of_passing) || '',\n                                                            onChange: (e)=>handleInputChange('twelfth_year_of_passing', e.target.value),\n                                                            className: \"w-20 p-1 border rounded text-sm\",\n                                                            placeholder: \"2020\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 630,\n                                                            columnNumber: 21\n                                                        }, this) : (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.twelfth_year_of_passing) ? \"\".concat(parseInt(editedStudent.twelfth_year_of_passing) - 2, \" - \").concat(editedStudent.twelfth_year_of_passing) : '-'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 628,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 597,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start mb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-6 w-full\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-500 text-sm w-[120px]\",\n                                                                            children: \"College :\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 648,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.twelfth_school) || '',\n                                                                            onChange: (e)=>handleInputChange('twelfth_school', e.target.value),\n                                                                            className: \"flex-1 p-1 border rounded text-sm\",\n                                                                            placeholder: \"School/College name\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 650,\n                                                                            columnNumber: 25\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-700 font-medium\",\n                                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.twelfth_school) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 658,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                    lineNumber: 647,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-500 text-sm w-[120px]\",\n                                                                            children: \"Board :\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 662,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.twelfth_board) || '',\n                                                                            onChange: (e)=>handleInputChange('twelfth_board', e.target.value),\n                                                                            className: \"flex-1 p-1 border rounded text-sm\",\n                                                                            placeholder: \"Board name\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 664,\n                                                                            columnNumber: 25\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-700 font-medium\",\n                                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.twelfth_board) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 672,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                    lineNumber: 661,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 646,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-500 text-sm w-[120px]\",\n                                                                            children: \"Location :\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 678,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.twelfth_location) || '',\n                                                                            onChange: (e)=>handleInputChange('twelfth_location', e.target.value),\n                                                                            className: \"flex-1 p-1 border rounded text-sm\",\n                                                                            placeholder: \"City, State\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 680,\n                                                                            columnNumber: 25\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-700 font-medium\",\n                                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.twelfth_location) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 688,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                    lineNumber: 677,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-500 text-sm w-[120px]\",\n                                                                            children: \"Specialization :\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 692,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.twelfth_specialization) || '',\n                                                                            onChange: (e)=>handleInputChange('twelfth_specialization', e.target.value),\n                                                                            className: \"flex-1 p-1 border rounded text-sm\",\n                                                                            placeholder: \"Science/Commerce/Arts\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 694,\n                                                                            columnNumber: 25\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-700 font-medium\",\n                                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.twelfth_specialization) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 702,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                    lineNumber: 691,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 676,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 645,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 644,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 596,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                        className: \"my-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 714,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-gray-800\",\n                                                                children: \"Class X\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                lineNumber: 720,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-blue-50 inline-flex items-center px-3 py-1 rounded-full ml-3\",\n                                                                children: isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.tenth_cgpa) || '',\n                                                                            onChange: (e)=>handleInputChange('tenth_cgpa', e.target.value),\n                                                                            className: \"w-16 p-1 border rounded text-sm text-blue-600 bg-transparent\",\n                                                                            placeholder: \"9.5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 724,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-500 ml-1\",\n                                                                            children: \"CGPA\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 731,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.tenth_percentage) || '',\n                                                                            onChange: (e)=>handleInputChange('tenth_percentage', e.target.value),\n                                                                            className: \"w-16 p-1 border rounded text-sm text-blue-600 bg-transparent ml-2\",\n                                                                            placeholder: \"95%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 732,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-blue-600 font-medium\",\n                                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.tenth_cgpa) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 742,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-500 ml-1\",\n                                                                            children: \"CGPA\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 743,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-blue-600 ml-2\",\n                                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.tenth_percentage) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 744,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                lineNumber: 721,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 719,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-lg font-semibold text-gray-800\",\n                                                        children: isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.tenth_year_of_passing) || '',\n                                                            onChange: (e)=>handleInputChange('tenth_year_of_passing', e.target.value),\n                                                            className: \"w-20 p-1 border rounded text-sm\",\n                                                            placeholder: \"2018\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 751,\n                                                            columnNumber: 21\n                                                        }, this) : (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.tenth_year_of_passing) ? \"\".concat(parseInt(editedStudent.tenth_year_of_passing) - 1, \" - \").concat(editedStudent.tenth_year_of_passing) : '-'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 749,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 718,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start mb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-6 w-full\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-500 text-sm w-[120px]\",\n                                                                            children: \"School :\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 770,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.tenth_school) || '',\n                                                                            onChange: (e)=>handleInputChange('tenth_school', e.target.value),\n                                                                            className: \"flex-1 p-1 border rounded text-sm\",\n                                                                            placeholder: \"School name\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 772,\n                                                                            columnNumber: 25\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-700 font-medium\",\n                                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.tenth_school) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 780,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                    lineNumber: 769,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-500 text-sm w-[120px]\",\n                                                                            children: \"Board :\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 784,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.tenth_board) || '',\n                                                                            onChange: (e)=>handleInputChange('tenth_board', e.target.value),\n                                                                            className: \"flex-1 p-1 border rounded text-sm\",\n                                                                            placeholder: \"Board name\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 786,\n                                                                            columnNumber: 25\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-700 font-medium\",\n                                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.tenth_board) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 794,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                    lineNumber: 783,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 768,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-500 text-sm w-[120px]\",\n                                                                            children: \"Location :\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 800,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.tenth_location) || '',\n                                                                            onChange: (e)=>handleInputChange('tenth_location', e.target.value),\n                                                                            className: \"flex-1 p-1 border rounded text-sm\",\n                                                                            placeholder: \"City, State\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 802,\n                                                                            columnNumber: 25\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-700 font-medium\",\n                                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.tenth_location) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 810,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                    lineNumber: 799,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-500 text-sm w-[120px]\",\n                                                                            children: \"Specialization :\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 814,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.tenth_specialization) || '',\n                                                                            onChange: (e)=>handleInputChange('tenth_specialization', e.target.value),\n                                                                            className: \"flex-1 p-1 border rounded text-sm\",\n                                                                            placeholder: \"General/Other\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 816,\n                                                                            columnNumber: 25\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-700 font-medium\",\n                                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.tenth_specialization) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 824,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                    lineNumber: 813,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 798,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 767,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 766,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 717,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                lineNumber: 523,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg p-5 shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-4 text-gray-800\",\n                                        children: \"Companies\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 835,\n                                        columnNumber: 13\n                                    }, this),\n                                    companyStats.loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center py-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBuilding_FaFileAlt_FaMapMarkerAlt_FaPhoneAlt_FaSpinner_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_6__.FaSpinner, {\n                                                className: \"animate-spin text-blue-500 text-xl mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 839,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Loading company data...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 840,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 838,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-500 text-sm\",\n                                                            children: \"Total Listings\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 847,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-semibold text-gray-700\",\n                                                            children: companyStats.totalListings\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 848,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 846,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 845,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-500 text-sm\",\n                                                            children: \"Eligible Jobs\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 855,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-semibold text-gray-700\",\n                                                            children: companyStats.eligibleJobs\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 856,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 854,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 853,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                lineNumber: 834,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                        lineNumber: 521,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-3 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg p-5 shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-4 text-gray-800\",\n                                        children: \"My Files\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 868,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center p-2 mb-3 cursor-pointer hover:bg-gray-50 rounded-lg transition-colors\",\n                                        onClick: ()=>setIsResumeModalOpen(true),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-blue-600\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"h-6 w-6\",\n                                                        fill: \"none\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        stroke: \"currentColor\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 877,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 876,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 875,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 874,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-grow\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-medium text-gray-700\",\n                                                        children: \"Resumes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 882,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: resumeCount > 0 ? \"\".concat(resumeCount, \" resume\").concat(resumeCount > 1 ? 's' : '', \" uploaded\") + (lastResumeUpdate ? \" • Last updated \".concat(new Date(lastResumeUpdate).toLocaleDateString()) : '') : 'No resumes uploaded'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 883,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 881,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-50 px-3 py-1 rounded-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-600 font-medium\",\n                                                    children: resumeCount\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 892,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 891,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 870,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center p-2 cursor-pointer hover:bg-gray-50 rounded-lg transition-colors\",\n                                        onClick: ()=>setIsDocumentsModalOpen(true),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-blue-600\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"h-6 w-6\",\n                                                        fill: \"none\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        stroke: \"currentColor\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 903,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 902,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 901,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 900,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-grow\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-medium text-gray-700\",\n                                                        children: \"Documents\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 908,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Academic certificates and marksheets\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 909,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 907,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-50 px-3 py-1 rounded-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-600 font-medium\",\n                                                    children: ((profile === null || profile === void 0 ? void 0 : profile.tenth_certificate) ? 1 : 0) + ((profile === null || profile === void 0 ? void 0 : profile.twelfth_certificate) ? 1 : 0) + semesterMarksheets.length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 912,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 911,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 896,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                lineNumber: 867,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg p-5 shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold text-gray-800\",\n                                            children: \"CURRENT ADDRESS\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 924,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 923,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500 w-20\",\n                                                        children: \"City\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 929,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-2\",\n                                                        children: \":\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 930,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.city) || '',\n                                                        onChange: (e)=>handleInputChange('city', e.target.value),\n                                                        className: \"flex-1 p-2 border rounded text-sm\",\n                                                        placeholder: \"City name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 932,\n                                                        columnNumber: 19\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-700\",\n                                                        children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.city) || '-'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 940,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 928,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500 w-20\",\n                                                        children: \"District\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 944,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-2\",\n                                                        children: \":\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 945,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.district) || '',\n                                                        onChange: (e)=>handleInputChange('district', e.target.value),\n                                                        className: \"flex-1 p-2 border rounded text-sm\",\n                                                        placeholder: \"District name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 947,\n                                                        columnNumber: 19\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-700\",\n                                                        children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.district) || '-'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 955,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 943,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500 w-20\",\n                                                        children: \"State\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 959,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-2\",\n                                                        children: \":\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 960,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.state) || '',\n                                                        onChange: (e)=>handleInputChange('state', e.target.value),\n                                                        className: \"flex-1 p-2 border rounded text-sm\",\n                                                        placeholder: \"State name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 962,\n                                                        columnNumber: 19\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-700\",\n                                                        children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.state) || '-'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 970,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 958,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500 w-20\",\n                                                        children: \"Pin Code\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 974,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-2\",\n                                                        children: \":\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 975,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.pincode) || '',\n                                                        onChange: (e)=>handleInputChange('pincode', e.target.value),\n                                                        className: \"flex-1 p-2 border rounded text-sm\",\n                                                        placeholder: \"Pin code\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 977,\n                                                        columnNumber: 19\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-700\",\n                                                        children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.pincode) || '-'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 985,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 973,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500 w-20\",\n                                                        children: \"Country\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 989,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-2\",\n                                                        children: \":\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 990,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.country) || '',\n                                                        onChange: (e)=>handleInputChange('country', e.target.value),\n                                                        className: \"flex-1 p-2 border rounded text-sm\",\n                                                        placeholder: \"Country name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 992,\n                                                        columnNumber: 19\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-700\",\n                                                        children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.country) || '-'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 1000,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 988,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500 w-20 mt-2\",\n                                                        children: \"Address\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 1004,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-2 mt-2\",\n                                                        children: \":\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 1005,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.address) || '',\n                                                        onChange: (e)=>handleInputChange('address', e.target.value),\n                                                        className: \"flex-1 p-2 border rounded text-sm\",\n                                                        rows: 3,\n                                                        placeholder: \"Full address\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 1007,\n                                                        columnNumber: 19\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-700 flex-1\",\n                                                        children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.address) || '-'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 1015,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 1003,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 927,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                lineNumber: 922,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                        lineNumber: 865,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ResumeModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        isOpen: isResumeModalOpen,\n                        onClose: ()=>setIsResumeModalOpen(false),\n                        resume: (profile === null || profile === void 0 ? void 0 : profile.resume_url) || (profile === null || profile === void 0 ? void 0 : profile.resume),\n                        onUpload: handleResumeUpload,\n                        onDelete: handleResumeDelete\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                        lineNumber: 1024,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DocumentsModal__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        isOpen: isDocumentsModalOpen,\n                        onClose: ()=>setIsDocumentsModalOpen(false),\n                        documents: {\n                            tenth: (profile === null || profile === void 0 ? void 0 : profile.tenth_certificate_url) || (profile === null || profile === void 0 ? void 0 : profile.tenth_certificate),\n                            twelfth: (profile === null || profile === void 0 ? void 0 : profile.twelfth_certificate_url) || (profile === null || profile === void 0 ? void 0 : profile.twelfth_certificate),\n                            semesterMarksheets: semesterMarksheets\n                        },\n                        onUploadCertificate: _api_students__WEBPACK_IMPORTED_MODULE_3__.studentsAPI.uploadCertificate,\n                        onUploadMarksheet: _api_students__WEBPACK_IMPORTED_MODULE_3__.studentsAPI.uploadSemesterMarksheet\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                        lineNumber: 1033,\n                        columnNumber: 7\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                lineNumber: 304,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n        lineNumber: 256,\n        columnNumber: 5\n    }, this);\n}\n_s(StudentProfile, \"ErkQrXcp6uhP342rBjEs2qSg9w8=\");\n_c = StudentProfile;\nvar _c;\n$RefreshReg$(_c, \"StudentProfile\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/student-management/StudentProfile.jsx\n"));

/***/ })

});