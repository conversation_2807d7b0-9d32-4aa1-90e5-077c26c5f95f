"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/student-management/page",{

/***/ "(app-pages-browser)/./src/app/admin/student-management/StudentProfile.jsx":
/*!*************************************************************!*\
  !*** ./src/app/admin/student-management/StudentProfile.jsx ***!
  \*************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StudentProfile)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Edit_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Edit,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Edit_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Edit,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Edit_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Edit,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Edit_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Edit,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_FaBuilding_FaFileAlt_FaMapMarkerAlt_FaPhoneAlt_FaSpinner_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=FaBuilding,FaFileAlt,FaMapMarkerAlt,FaPhoneAlt,FaSpinner,FaUser!=!react-icons/fa */ \"(app-pages-browser)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var _api_students__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../api/students */ \"(app-pages-browser)/./src/api/students.js\");\n/* harmony import */ var _DocumentsModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./DocumentsModal */ \"(app-pages-browser)/./src/app/admin/student-management/DocumentsModal.jsx\");\n/* harmony import */ var _ResumeModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ResumeModal */ \"(app-pages-browser)/./src/app/admin/student-management/ResumeModal.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction StudentProfile(param) {\n    let { selectedStudent, editedStudent, isEditing, handleBackToList, handleEdit, handleSave, handleCancel, handleInputChange, departmentOptions } = param;\n    _s();\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [semesterMarksheets, setSemesterMarksheets] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isDocumentsModalOpen, setIsDocumentsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isResumeModalOpen, setIsResumeModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [companyStats, setCompanyStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        loading: false,\n        totalListings: 0,\n        eligibleJobs: 0\n    });\n    const [resumeCount, setResumeCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [lastResumeUpdate, setLastResumeUpdate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Use selectedStudent as the profile data, but allow fetching more details if needed\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentProfile.useEffect\": ()=>{\n            if (selectedStudent) {\n                setProfile(selectedStudent);\n                // Optionally fetch additional details if needed\n                fetchAdditionalDetails(selectedStudent.id);\n                // Fetch company statistics\n                fetchCompanyStats(selectedStudent.id);\n                // Fetch resume information\n                fetchResumeInfo(selectedStudent.id);\n            }\n        }\n    }[\"StudentProfile.useEffect\"], [\n        selectedStudent\n    ]);\n    // Fetch additional details if needed\n    const fetchAdditionalDetails = async (studentId)=>{\n        if (!studentId) return;\n        try {\n            setLoading(true);\n            // This could fetch semester marksheets or other detailed info not included in the list view\n            // const details = await studentsAPI.getStudentDetails(studentId);\n            // setSemesterMarksheets(details.semesterMarksheets || []);\n            setLoading(false);\n        } catch (err) {\n            console.error('Error fetching student details:', err);\n            setLoading(false);\n        }\n    };\n    // Fetch company statistics for the student\n    const fetchCompanyStats = async (studentId)=>{\n        if (!studentId) return;\n        try {\n            setCompanyStats((prev)=>({\n                    ...prev,\n                    loading: true\n                }));\n            // Mock implementation - replace with actual API call\n            // const stats = await studentsAPI.getStudentCompanyStats(studentId);\n            // For now, provide mock data\n            const mockStats = {\n                loading: false,\n                totalListings: 25,\n                eligibleJobs: 18\n            };\n            setCompanyStats(mockStats);\n        } catch (err) {\n            console.error('Error fetching company stats:', err);\n            setCompanyStats({\n                loading: false,\n                totalListings: 0,\n                eligibleJobs: 0\n            });\n        }\n    };\n    // Fetch resume information for the student\n    const fetchResumeInfo = async (studentId)=>{\n        if (!studentId) return;\n        try {\n            // Mock implementation - replace with actual API call\n            // const resumeInfo = await studentsAPI.getStudentResumes(studentId);\n            // For now, provide mock data\n            const mockResumeCount = 2;\n            const mockLastUpdate = new Date('2024-01-15').toISOString();\n            setResumeCount(mockResumeCount);\n            setLastResumeUpdate(mockLastUpdate);\n        } catch (err) {\n            console.error('Error fetching resume info:', err);\n            setResumeCount(0);\n            setLastResumeUpdate(null);\n        }\n    };\n    // Calculate overall CGPA\n    const calculateOverallCGPA = ()=>{\n        return (profile === null || profile === void 0 ? void 0 : profile.cgpa) || 'N/A';\n    };\n    // Calculate percentage from CGPA (approximation)\n    const calculatePercentage = (cgpa)=>{\n        if (!cgpa || cgpa === 'N/A') return '';\n        const numericCgpa = parseFloat(cgpa);\n        if (isNaN(numericCgpa)) return '';\n        return (numericCgpa * 9.5).toFixed(2) + '%';\n    };\n    // Format year range if available\n    const formatEducationPeriod = (joiningYear, passoutYear)=>{\n        if (joiningYear && passoutYear) {\n            return \"\".concat(joiningYear, \" - \").concat(passoutYear);\n        }\n        return 'N/A';\n    };\n    // Get year range for student\n    const getYearRange = ()=>{\n        if ((profile === null || profile === void 0 ? void 0 : profile.joining_year) && (profile === null || profile === void 0 ? void 0 : profile.passout_year)) {\n            return \"\".concat(profile.joining_year, \" - \").concat(profile.passout_year);\n        }\n        if ((editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.joining_year) && (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.passout_year)) {\n            return \"\".concat(editedStudent.joining_year, \" - \").concat(editedStudent.passout_year);\n        }\n        if ((profile === null || profile === void 0 ? void 0 : profile.year) || (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.year)) {\n            return (profile === null || profile === void 0 ? void 0 : profile.year) || (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.year);\n        }\n        return null;\n    };\n    // Get semester CGPA (mock implementation)\n    const getSemesterCGPA = (semester)=>{\n        var _semesterMarksheets_;\n        return ((_semesterMarksheets_ = semesterMarksheets[semester - 1]) === null || _semesterMarksheets_ === void 0 ? void 0 : _semesterMarksheets_.cgpa) || '-';\n    };\n    // Handle resume upload\n    const handleResumeUpload = async (file)=>{\n        try {\n            await _api_students__WEBPACK_IMPORTED_MODULE_3__.studentsAPI.uploadResume(file);\n            // Refresh resume info after upload\n            if (selectedStudent) {\n                fetchResumeInfo(selectedStudent.id);\n            }\n        } catch (err) {\n            console.error('Error uploading resume:', err);\n            alert('Failed to upload resume. Please try again.');\n        }\n    };\n    // Handle resume delete\n    const handleResumeDelete = async (resumeId)=>{\n        try {\n            await _api_students__WEBPACK_IMPORTED_MODULE_3__.studentsAPI.deleteResume(resumeId);\n            // Refresh resume info after delete\n            if (selectedStudent) {\n                fetchResumeInfo(selectedStudent.id);\n            }\n        } catch (err) {\n            console.error('Error deleting resume:', err);\n            alert('Failed to delete resume. Please try again.');\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBuilding_FaFileAlt_FaMapMarkerAlt_FaPhoneAlt_FaSpinner_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_6__.FaSpinner, {\n                    className: \"animate-spin text-blue-500 text-xl mr-2\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                    lineNumber: 187,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: \"Loading details...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                    lineNumber: 188,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n            lineNumber: 186,\n            columnNumber: 7\n        }, this);\n    }\n    if (!selectedStudent && !editedStudent) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center p-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-500\",\n                    children: \"No student selected\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                    lineNumber: 196,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleBackToList,\n                    className: \"mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                    children: \"Back to List\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                    lineNumber: 197,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n            lineNumber: 195,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleBackToList,\n                        className: \"flex items-center gap-2 text-blue-600 hover:text-blue-800 transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Edit_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                lineNumber: 215,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Back to List\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                lineNumber: 216,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleSave,\n                                    className: \"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors mr-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Edit_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Save\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleCancel,\n                                    className: \"px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Edit_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleEdit,\n                            className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Edit_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Edit\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                lineNumber: 246,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                            lineNumber: 242,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                        lineNumber: 219,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                lineNumber: 210,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-xl shadow-sm overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-blue-50 p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-16 h-16 bg-blue-500 text-white flex items-center justify-center rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.name) ? editedStudent.name[0].toUpperCase() : 'S'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-grow\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-xl font-semibold text-gray-800\",\n                                            children: isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.name) || '',\n                                                onChange: (e)=>handleInputChange('name', e.target.value),\n                                                className: \"w-full p-1 border rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 19\n                                            }, this) : (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.name) || 'Unknown Student'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1 text-gray-600\",\n                                            children: [\n                                                isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.rollNumber) || '',\n                                                    onChange: (e)=>handleInputChange('rollNumber', e.target.value),\n                                                    className: \"p-1 border rounded mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 19\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        \"ID: \",\n                                                        (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.rollNumber) || 'N/A'\n                                                    ]\n                                                }, void 0, true),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mx-2\",\n                                                    children: \"|\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 17\n                                                }, this),\n                                                isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.department) || '',\n                                                    onChange: (e)=>handleInputChange('department', e.target.value),\n                                                    className: \"p-1 border rounded mr-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"\",\n                                                            children: \"Select Department\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        departmentOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: option.value,\n                                                                children: option.label\n                                                            }, option.value, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                lineNumber: 303,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 19\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        \"Department: \",\n                                                        (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.department) || 'N/A'\n                                                    ]\n                                                }, void 0, true),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mx-2\",\n                                                    children: \"|\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 17\n                                                }, this),\n                                                isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.year) || '',\n                                                    onChange: (e)=>handleInputChange('year', e.target.value),\n                                                    className: \"p-1 border rounded\",\n                                                    placeholder: \"YYYY-YYYY\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 19\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        \"Year: \",\n                                                        getYearRange() || 'N/A'\n                                                    ]\n                                                }, void 0, true)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-100 px-3 py-1 rounded-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-blue-600 font-medium\",\n                                                children: [\n                                                    \"CGPA: \",\n                                                    calculateOverallCGPA()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 17\n                                            }, this),\n                                            calculateOverallCGPA() !== 'N/A' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500 ml-1\",\n                                                children: [\n                                                    \"(\",\n                                                    calculatePercentage(calculateOverallCGPA()),\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                            lineNumber: 259,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                        lineNumber: 258,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-medium mb-4 text-gray-800 border-b pb-2\",\n                                            children: \"Personal Information\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 344,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-gray-600 text-sm mb-1\",\n                                                            children: \"Email\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 348,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"email\",\n                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.email) || '',\n                                                            onChange: (e)=>handleInputChange('email', e.target.value),\n                                                            className: \"w-full p-2 border rounded-lg\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-800\",\n                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.email) || 'N/A'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 357,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-gray-600 text-sm mb-1\",\n                                                            children: \"Phone\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 362,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"tel\",\n                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.phone) || '',\n                                                            onChange: (e)=>handleInputChange('phone', e.target.value),\n                                                            className: \"w-full p-2 border rounded-lg\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 364,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-800\",\n                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.phone) || 'N/A'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 371,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-gray-600 text-sm mb-1\",\n                                                            children: \"Date of Birth\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"date\",\n                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.dateOfBirth) || '',\n                                                            onChange: (e)=>handleInputChange('dateOfBirth', e.target.value),\n                                                            className: \"w-full p-2 border rounded-lg\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 378,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-800\",\n                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.dateOfBirth) || 'N/A'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 385,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-gray-600 text-sm mb-1\",\n                                                            children: \"Parent Contact\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 390,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"tel\",\n                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.parentContact) || '',\n                                                            onChange: (e)=>handleInputChange('parentContact', e.target.value),\n                                                            className: \"w-full p-2 border rounded-lg\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 392,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-800\",\n                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.parentContact) || 'N/A'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 399,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                    lineNumber: 343,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-medium mb-4 text-gray-800 border-b pb-2\",\n                                            children: \"Address & Education\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-gray-600 text-sm mb-1\",\n                                                            children: \"Address\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 411,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.address) || '',\n                                                            onChange: (e)=>handleInputChange('address', e.target.value),\n                                                            className: \"w-full p-2 border rounded-lg\",\n                                                            rows: 2\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 413,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-800\",\n                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.address) || 'N/A'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 420,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 410,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-gray-600 text-sm mb-1\",\n                                                            children: \"Education\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 425,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.education) || '',\n                                                            onChange: (e)=>handleInputChange('education', e.target.value),\n                                                            className: \"w-full p-2 border rounded-lg\",\n                                                            rows: 2\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 427,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-800\",\n                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.education) || 'N/A'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 434,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                    lineNumber: 406,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"md:col-span-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-medium mb-4 text-gray-800 border-b pb-2\",\n                                            children: \"Skills\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 442,\n                                            columnNumber: 15\n                                        }, this),\n                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: Array.isArray(editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.skills) ? editedStudent.skills.join(', ') : (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.skills) || '',\n                                            onChange: (e)=>handleInputChange('skills', e.target.value.split(',').map((s)=>s.trim())),\n                                            className: \"w-full p-2 border rounded-lg\",\n                                            rows: 2,\n                                            placeholder: \"Enter skills separated by commas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 445,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: Array.isArray(editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.skills) && editedStudent.skills.length > 0 ? editedStudent.skills.map((skill, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm\",\n                                                    children: skill\n                                                }, index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 456,\n                                                    columnNumber: 23\n                                                }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"No skills listed\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 464,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 453,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                    lineNumber: 441,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                            lineNumber: 341,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                        lineNumber: 340,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-6 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg p-5 shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-6 text-gray-800\",\n                                        children: \"Academic\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-800\",\n                                                        children: \"Semester Wise score\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-blue-50 inline-flex items-center px-3 py-1 rounded-full ml-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-blue-600 font-medium\",\n                                                                children: calculateOverallCGPA()\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                lineNumber: 481,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-500 ml-1\",\n                                                                children: \"CGPA\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                lineNumber: 482,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-blue-600 ml-2\",\n                                                                children: calculatePercentage(calculateOverallCGPA())\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                lineNumber: 483,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 480,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 478,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-lg font-semibold text-gray-800\",\n                                                children: isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.joining_year) || '',\n                                                            onChange: (e)=>handleInputChange('joining_year', e.target.value),\n                                                            className: \"w-20 p-1 border rounded text-sm\",\n                                                            placeholder: \"2020\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 489,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"-\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 496,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.passout_year) || '',\n                                                            onChange: (e)=>handleInputChange('passout_year', e.target.value),\n                                                            className: \"w-20 p-1 border rounded text-sm\",\n                                                            placeholder: \"2024\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 497,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 488,\n                                                    columnNumber: 21\n                                                }, this) : formatEducationPeriod(editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.joining_year, editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.passout_year)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 486,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 477,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"overflow-x-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                className: \"w-full border-collapse border border-gray-300\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            className: \"bg-gray-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"border border-gray-300 px-4 py-3 text-sm font-semibold text-gray-700\",\n                                                                    children: \"Sem\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                    lineNumber: 516,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                [\n                                                                    1,\n                                                                    2,\n                                                                    3,\n                                                                    4,\n                                                                    5,\n                                                                    6,\n                                                                    7,\n                                                                    8\n                                                                ].map((sem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"border border-gray-300 px-4 py-3 text-sm font-semibold text-gray-700\",\n                                                                        children: sem\n                                                                    }, sem, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                        lineNumber: 518,\n                                                                        columnNumber: 25\n                                                                    }, this))\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 515,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 514,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"border border-gray-300 px-4 py-3 text-sm font-medium text-gray-700\",\n                                                                    children: \"Cgpa\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                    lineNumber: 524,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                [\n                                                                    1,\n                                                                    2,\n                                                                    3,\n                                                                    4,\n                                                                    5,\n                                                                    6,\n                                                                    7,\n                                                                    8\n                                                                ].map((sem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"border border-gray-300 px-4 py-3 text-sm text-gray-700\",\n                                                                        children: getSemesterCGPA(sem)\n                                                                    }, sem, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                        lineNumber: 526,\n                                                                        columnNumber: 25\n                                                                    }, this))\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 523,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 522,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 513,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 512,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 511,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                        className: \"my-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 535,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-gray-800\",\n                                                                children: \"Class XII\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                lineNumber: 541,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-blue-50 inline-flex items-center px-3 py-1 rounded-full ml-3\",\n                                                                children: isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.twelfth_cgpa) || '',\n                                                                            onChange: (e)=>handleInputChange('twelfth_cgpa', e.target.value),\n                                                                            className: \"w-16 p-1 border rounded text-sm text-blue-600 bg-transparent\",\n                                                                            placeholder: \"9.5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 545,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-500 ml-1\",\n                                                                            children: \"CGPA\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 552,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.twelfth_percentage) || '',\n                                                                            onChange: (e)=>handleInputChange('twelfth_percentage', e.target.value),\n                                                                            className: \"w-16 p-1 border rounded text-sm text-blue-600 bg-transparent ml-2\",\n                                                                            placeholder: \"95%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 553,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-blue-600 font-medium\",\n                                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.twelfth_cgpa) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 563,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-500 ml-1\",\n                                                                            children: \"CGPA\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 564,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-blue-600 ml-2\",\n                                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.twelfth_percentage) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 565,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                lineNumber: 542,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 540,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-lg font-semibold text-gray-800\",\n                                                        children: isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.twelfth_year_of_passing) || '',\n                                                            onChange: (e)=>handleInputChange('twelfth_year_of_passing', e.target.value),\n                                                            className: \"w-20 p-1 border rounded text-sm\",\n                                                            placeholder: \"2020\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 572,\n                                                            columnNumber: 21\n                                                        }, this) : (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.twelfth_year_of_passing) ? \"\".concat(parseInt(editedStudent.twelfth_year_of_passing) - 2, \" - \").concat(editedStudent.twelfth_year_of_passing) : '-'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 570,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 539,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start mb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-6 w-full\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-500 text-sm w-[120px]\",\n                                                                            children: \"College :\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 590,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.twelfth_school) || '',\n                                                                            onChange: (e)=>handleInputChange('twelfth_school', e.target.value),\n                                                                            className: \"flex-1 p-1 border rounded text-sm\",\n                                                                            placeholder: \"School/College name\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 592,\n                                                                            columnNumber: 25\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-700 font-medium\",\n                                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.twelfth_school) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 600,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                    lineNumber: 589,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-500 text-sm w-[120px]\",\n                                                                            children: \"Board :\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 604,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.twelfth_board) || '',\n                                                                            onChange: (e)=>handleInputChange('twelfth_board', e.target.value),\n                                                                            className: \"flex-1 p-1 border rounded text-sm\",\n                                                                            placeholder: \"Board name\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 606,\n                                                                            columnNumber: 25\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-700 font-medium\",\n                                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.twelfth_board) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 614,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                    lineNumber: 603,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 588,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-500 text-sm w-[120px]\",\n                                                                            children: \"Location :\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 620,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.twelfth_location) || '',\n                                                                            onChange: (e)=>handleInputChange('twelfth_location', e.target.value),\n                                                                            className: \"flex-1 p-1 border rounded text-sm\",\n                                                                            placeholder: \"City, State\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 622,\n                                                                            columnNumber: 25\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-700 font-medium\",\n                                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.twelfth_location) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 630,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                    lineNumber: 619,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-500 text-sm w-[120px]\",\n                                                                            children: \"Specialization :\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 634,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.twelfth_specialization) || '',\n                                                                            onChange: (e)=>handleInputChange('twelfth_specialization', e.target.value),\n                                                                            className: \"flex-1 p-1 border rounded text-sm\",\n                                                                            placeholder: \"Science/Commerce/Arts\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 636,\n                                                                            columnNumber: 25\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-700 font-medium\",\n                                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.twelfth_specialization) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 644,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                    lineNumber: 633,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 618,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 587,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 586,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 538,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                        className: \"my-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 656,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-gray-800\",\n                                                                children: \"Class X\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                lineNumber: 662,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-blue-50 inline-flex items-center px-3 py-1 rounded-full ml-3\",\n                                                                children: isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.tenth_cgpa) || '',\n                                                                            onChange: (e)=>handleInputChange('tenth_cgpa', e.target.value),\n                                                                            className: \"w-16 p-1 border rounded text-sm text-blue-600 bg-transparent\",\n                                                                            placeholder: \"9.5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 666,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-500 ml-1\",\n                                                                            children: \"CGPA\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 673,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.tenth_percentage) || '',\n                                                                            onChange: (e)=>handleInputChange('tenth_percentage', e.target.value),\n                                                                            className: \"w-16 p-1 border rounded text-sm text-blue-600 bg-transparent ml-2\",\n                                                                            placeholder: \"95%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 674,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-blue-600 font-medium\",\n                                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.tenth_cgpa) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 684,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-500 ml-1\",\n                                                                            children: \"CGPA\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 685,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-blue-600 ml-2\",\n                                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.tenth_percentage) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 686,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                lineNumber: 663,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 661,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-lg font-semibold text-gray-800\",\n                                                        children: isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.tenth_year_of_passing) || '',\n                                                            onChange: (e)=>handleInputChange('tenth_year_of_passing', e.target.value),\n                                                            className: \"w-20 p-1 border rounded text-sm\",\n                                                            placeholder: \"2018\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 693,\n                                                            columnNumber: 21\n                                                        }, this) : (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.tenth_year_of_passing) ? \"\".concat(parseInt(editedStudent.tenth_year_of_passing) - 1, \" - \").concat(editedStudent.tenth_year_of_passing) : '-'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 691,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 660,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start mb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-6 w-full\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-500 text-sm w-[120px]\",\n                                                                            children: \"School :\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 712,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.tenth_school) || '',\n                                                                            onChange: (e)=>handleInputChange('tenth_school', e.target.value),\n                                                                            className: \"flex-1 p-1 border rounded text-sm\",\n                                                                            placeholder: \"School name\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 714,\n                                                                            columnNumber: 25\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-700 font-medium\",\n                                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.tenth_school) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 722,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                    lineNumber: 711,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-500 text-sm w-[120px]\",\n                                                                            children: \"Board :\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 726,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.tenth_board) || '',\n                                                                            onChange: (e)=>handleInputChange('tenth_board', e.target.value),\n                                                                            className: \"flex-1 p-1 border rounded text-sm\",\n                                                                            placeholder: \"Board name\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 728,\n                                                                            columnNumber: 25\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-700 font-medium\",\n                                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.tenth_board) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 736,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                    lineNumber: 725,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 710,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-500 text-sm w-[120px]\",\n                                                                            children: \"Location :\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 742,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.tenth_location) || '',\n                                                                            onChange: (e)=>handleInputChange('tenth_location', e.target.value),\n                                                                            className: \"flex-1 p-1 border rounded text-sm\",\n                                                                            placeholder: \"City, State\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 744,\n                                                                            columnNumber: 25\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-700 font-medium\",\n                                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.tenth_location) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 752,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                    lineNumber: 741,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-500 text-sm w-[120px]\",\n                                                                            children: \"Specialization :\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 756,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.tenth_specialization) || '',\n                                                                            onChange: (e)=>handleInputChange('tenth_specialization', e.target.value),\n                                                                            className: \"flex-1 p-1 border rounded text-sm\",\n                                                                            placeholder: \"General/Other\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 758,\n                                                                            columnNumber: 25\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-700 font-medium\",\n                                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.tenth_specialization) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 766,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                    lineNumber: 755,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 740,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 709,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 708,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 659,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                lineNumber: 475,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg p-5 shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-4 text-gray-800\",\n                                        children: \"Companies\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 777,\n                                        columnNumber: 13\n                                    }, this),\n                                    companyStats.loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center py-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBuilding_FaFileAlt_FaMapMarkerAlt_FaPhoneAlt_FaSpinner_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_6__.FaSpinner, {\n                                                className: \"animate-spin text-blue-500 text-xl mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 781,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Loading company data...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 782,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 780,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-500 text-sm\",\n                                                            children: \"Total Listings\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 789,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-semibold text-gray-700\",\n                                                            children: companyStats.totalListings\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 790,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 788,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 787,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-500 text-sm\",\n                                                            children: \"Eligible Jobs\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 797,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-semibold text-gray-700\",\n                                                            children: companyStats.eligibleJobs\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 798,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 796,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 795,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                lineNumber: 776,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                        lineNumber: 473,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-3 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg p-5 shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-4 text-gray-800\",\n                                        children: \"My Files\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 810,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center p-2 mb-3 cursor-pointer hover:bg-gray-50 rounded-lg transition-colors\",\n                                        onClick: ()=>setIsResumeModalOpen(true),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-blue-600\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"h-6 w-6\",\n                                                        fill: \"none\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        stroke: \"currentColor\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 819,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 818,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 817,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 816,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-grow\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-medium text-gray-700\",\n                                                        children: \"Resumes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 824,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: resumeCount > 0 ? \"\".concat(resumeCount, \" resume\").concat(resumeCount > 1 ? 's' : '', \" uploaded\") + (lastResumeUpdate ? \" • Last updated \".concat(new Date(lastResumeUpdate).toLocaleDateString()) : '') : 'No resumes uploaded'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 825,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 823,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-50 px-3 py-1 rounded-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-600 font-medium\",\n                                                    children: resumeCount\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 834,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 833,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 812,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center p-2 cursor-pointer hover:bg-gray-50 rounded-lg transition-colors\",\n                                        onClick: ()=>setIsDocumentsModalOpen(true),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-blue-600\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"h-6 w-6\",\n                                                        fill: \"none\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        stroke: \"currentColor\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 845,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 844,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 843,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 842,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-grow\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-medium text-gray-700\",\n                                                        children: \"Documents\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 850,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Academic certificates and marksheets\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 851,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 849,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-50 px-3 py-1 rounded-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-600 font-medium\",\n                                                    children: ((profile === null || profile === void 0 ? void 0 : profile.tenth_certificate) ? 1 : 0) + ((profile === null || profile === void 0 ? void 0 : profile.twelfth_certificate) ? 1 : 0) + semesterMarksheets.length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 854,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 853,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 838,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                lineNumber: 809,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg p-5 shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold text-gray-800\",\n                                            children: \"CURRENT ADDRESS\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 866,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 865,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500 w-20\",\n                                                        children: \"City\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 871,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-2\",\n                                                        children: \":\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 872,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.city) || '',\n                                                        onChange: (e)=>handleInputChange('city', e.target.value),\n                                                        className: \"flex-1 p-2 border rounded text-sm\",\n                                                        placeholder: \"City name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 874,\n                                                        columnNumber: 19\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-700\",\n                                                        children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.city) || '-'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 882,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 870,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500 w-20\",\n                                                        children: \"District\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 886,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-2\",\n                                                        children: \":\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 887,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.district) || '',\n                                                        onChange: (e)=>handleInputChange('district', e.target.value),\n                                                        className: \"flex-1 p-2 border rounded text-sm\",\n                                                        placeholder: \"District name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 889,\n                                                        columnNumber: 19\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-700\",\n                                                        children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.district) || '-'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 897,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 885,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500 w-20\",\n                                                        children: \"State\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 901,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-2\",\n                                                        children: \":\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 902,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.state) || '',\n                                                        onChange: (e)=>handleInputChange('state', e.target.value),\n                                                        className: \"flex-1 p-2 border rounded text-sm\",\n                                                        placeholder: \"State name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 904,\n                                                        columnNumber: 19\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-700\",\n                                                        children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.state) || '-'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 912,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 900,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500 w-20\",\n                                                        children: \"Pin Code\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 916,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-2\",\n                                                        children: \":\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 917,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.pincode) || '',\n                                                        onChange: (e)=>handleInputChange('pincode', e.target.value),\n                                                        className: \"flex-1 p-2 border rounded text-sm\",\n                                                        placeholder: \"Pin code\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 919,\n                                                        columnNumber: 19\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-700\",\n                                                        children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.pincode) || '-'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 927,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 915,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500 w-20\",\n                                                        children: \"Country\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 931,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-2\",\n                                                        children: \":\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 932,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.country) || '',\n                                                        onChange: (e)=>handleInputChange('country', e.target.value),\n                                                        className: \"flex-1 p-2 border rounded text-sm\",\n                                                        placeholder: \"Country name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 934,\n                                                        columnNumber: 19\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-700\",\n                                                        children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.country) || '-'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 942,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 930,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500 w-20 mt-2\",\n                                                        children: \"Address\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 946,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-2 mt-2\",\n                                                        children: \":\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 947,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.address) || '',\n                                                        onChange: (e)=>handleInputChange('address', e.target.value),\n                                                        className: \"flex-1 p-2 border rounded text-sm\",\n                                                        rows: 3,\n                                                        placeholder: \"Full address\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 949,\n                                                        columnNumber: 19\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-700 flex-1\",\n                                                        children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.address) || '-'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 957,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 945,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 869,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                lineNumber: 864,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                        lineNumber: 807,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ResumeModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        isOpen: isResumeModalOpen,\n                        onClose: ()=>setIsResumeModalOpen(false),\n                        resume: (profile === null || profile === void 0 ? void 0 : profile.resume_url) || (profile === null || profile === void 0 ? void 0 : profile.resume),\n                        onUpload: handleResumeUpload,\n                        onDelete: handleResumeDelete\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                        lineNumber: 966,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DocumentsModal__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        isOpen: isDocumentsModalOpen,\n                        onClose: ()=>setIsDocumentsModalOpen(false),\n                        documents: {\n                            tenth: (profile === null || profile === void 0 ? void 0 : profile.tenth_certificate_url) || (profile === null || profile === void 0 ? void 0 : profile.tenth_certificate),\n                            twelfth: (profile === null || profile === void 0 ? void 0 : profile.twelfth_certificate_url) || (profile === null || profile === void 0 ? void 0 : profile.twelfth_certificate),\n                            semesterMarksheets: semesterMarksheets\n                        },\n                        onUploadCertificate: _api_students__WEBPACK_IMPORTED_MODULE_3__.studentsAPI.uploadCertificate,\n                        onUploadMarksheet: _api_students__WEBPACK_IMPORTED_MODULE_3__.studentsAPI.uploadSemesterMarksheet\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                        lineNumber: 975,\n                        columnNumber: 7\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                lineNumber: 256,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n        lineNumber: 208,\n        columnNumber: 5\n    }, this);\n}\n_s(StudentProfile, \"ErkQrXcp6uhP342rBjEs2qSg9w8=\");\n_c = StudentProfile;\nvar _c;\n$RefreshReg$(_c, \"StudentProfile\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/student-management/StudentProfile.jsx\n"));

/***/ })

});