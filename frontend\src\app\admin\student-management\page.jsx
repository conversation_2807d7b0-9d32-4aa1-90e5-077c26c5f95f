'use client';

import {
  ArrowLeft,
  Calendar,
  RefreshCw,
  Save,
  Search,
  User,
  X,
  GraduationCap
} from "lucide-react";
import { useEffect, useRef, useState } from 'react';
import { studentsAPI } from '../../../api/students';
import { getAuthToken } from '../../../api/auth';
import CustomDropdown from './StudentDropdown';
import StudentProfile from './StudentProfile';
import DepartmentCards from './DepartmentCards';
import PassoutYearCards from './PassoutYearCards';
import StudentList from './StudentList';

export default function StudentManagement() {
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [selectedDepartment, setSelectedDepartment] = useState(null);
  const [selectedYear, setSelectedYear] = useState('all');
  const [selectedStudent, setSelectedStudent] = useState(null);
  const [isEditing, setIsEditing] = useState(false);
  const [editedStudent, setEditedStudent] = useState(null);
  const dropdownRef = useRef(null);
  const [isYearDropdownOpen, setIsYearDropdownOpen] = useState(false);
  const [students, setStudents] = useState([]);
  const [allStudents, setAllStudents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isRetrying, setIsRetrying] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalStudents, setTotalStudents] = useState(0);
  const [availableYears, setAvailableYears] = useState([]);
  const [departmentStats, setDepartmentStats] = useState([]);
  const [selectedPassoutYear, setSelectedPassoutYear] = useState(null);
  const [cgpaMin, setCgpaMin] = useState('');
  const [cgpaMax, setCgpaMax] = useState('');

  // Dropdown options
  const departmentOptions = [
    { value: 'Computer Science', label: 'Computer Science' },
    { value: 'Electronics', label: 'Electronics' },
    { value: 'Mechanical', label: 'Mechanical' },
    { value: 'Civil', label: 'Civil' },
    { value: 'Electrical', label: 'Electrical' },
    { value: 'Information Technology', label: 'Information Technology' },
    { value: 'Chemical', label: 'Chemical' },
    { value: 'Biotechnology', label: 'Biotechnology' }
  ];

  // Fetch all students for complete dataset
  const fetchAllStudents = async () => {
    try {
      const token = getAuthToken();
      if (!token) {
        throw new Error('No authentication token found. Please login first.');
      }

      let allData = [];
      let page = 1;
      let hasMore = true;

      while (hasMore) {
        const response = await studentsAPI.getStudents({ page, page_size: 100 });
        allData = [...allData, ...response.data];
        
        if (response.pagination) {
          hasMore = page < response.pagination.total_pages;
          page++;
        } else {
          hasMore = false;
        }
      }

      const studentsData = allData.map(student => ({
        id: student.id,
        rollNumber: student.student_id || 'N/A',
        name: `${student.first_name || ''} ${student.last_name || ''}`.trim() || 'Unknown',
        email: student.contact_email || student.email || 'N/A',
        phone: student.phone || 'N/A',
        department: student.branch || 'N/A',
        year: getYearFromBranch(student.branch, student),
        cgpa: student.gpa || 'N/A',
        address: student.address || 'N/A',
        dateOfBirth: student.date_of_birth || '',
        parentContact: student.parent_contact || 'N/A',
        education: student.education || 'N/A',
        skills: student.skills || [],

        // Academic details
        joining_year: student.joining_year || student.admission_year || '',
        passout_year: student.passout_year || student.graduation_year || '',

        // Class XII details
        twelfth_cgpa: student.twelfth_cgpa || student.class_12_cgpa || '',
        twelfth_percentage: student.twelfth_percentage || student.class_12_percentage || '',
        twelfth_year_of_passing: student.twelfth_year_of_passing || student.class_12_year || '',
        twelfth_school: student.twelfth_school || student.class_12_school || '',
        twelfth_board: student.twelfth_board || student.class_12_board || '',
        twelfth_location: student.twelfth_location || student.class_12_location || '',
        twelfth_specialization: student.twelfth_specialization || student.class_12_stream || '',

        // Class X details
        tenth_cgpa: student.tenth_cgpa || student.class_10_cgpa || '',
        tenth_percentage: student.tenth_percentage || student.class_10_percentage || '',
        tenth_year_of_passing: student.tenth_year_of_passing || student.class_10_year || '',
        tenth_school: student.tenth_school || student.class_10_school || '',
        tenth_board: student.tenth_board || student.class_10_board || '',
        tenth_location: student.tenth_location || student.class_10_location || '',
        tenth_specialization: student.tenth_specialization || student.class_10_stream || '',

        // Address details
        city: student.city || '',
        district: student.district || '',
        state: student.state || '',
        pincode: student.pincode || student.pin_code || '',
        country: student.country || 'India',

        // Certificate URLs
        tenth_certificate: student.tenth_certificate || student.class_10_certificate || '',
        twelfth_certificate: student.twelfth_certificate || student.class_12_certificate || '',
        tenth_certificate_url: student.tenth_certificate_url || student.class_10_certificate_url || '',
        twelfth_certificate_url: student.twelfth_certificate_url || student.class_12_certificate_url || '',

        // Resume details
        resume: student.resume || '',
        resume_url: student.resume_url || '',

        // Add some mock data if fields are empty (for testing purposes)
        ...((!student.city && !student.twelfth_cgpa) ? {
          city: 'Mumbai',
          district: 'Mumbai',
          state: 'Maharashtra',
          pincode: '400001',
          country: 'India',
          joining_year: '2020',
          passout_year: '2024',
          twelfth_cgpa: '9.2',
          twelfth_percentage: '87.4%',
          twelfth_year_of_passing: '2020',
          twelfth_school: 'ABC Junior College',
          twelfth_board: 'Maharashtra State Board',
          twelfth_location: 'Mumbai, Maharashtra',
          twelfth_specialization: 'Science',
          tenth_cgpa: '9.0',
          tenth_percentage: '85.6%',
          tenth_year_of_passing: '2018',
          tenth_school: 'XYZ High School',
          tenth_board: 'Maharashtra State Board',
          tenth_location: 'Mumbai, Maharashtra',
          tenth_specialization: 'General'
        } : {})
      }));

      setAllStudents(studentsData);
      setAvailableYears(getAvailableYears(studentsData));
      setDepartmentStats(getDepartmentStats(studentsData));
      
      return studentsData;
    } catch (err) {
      console.error('Error fetching all students:', err);
      throw err;
    }
  };

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 300); // 300ms delay

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Fetch students from Django backend with pagination
  const fetchStudents = async (page = 1) => {
    try {
      setLoading(true);
      setError(null);
      setIsRetrying(false);
      
      const token = getAuthToken();
      if (!token) {
        throw new Error('No authentication token found. Please login first.');
      }

      // Use allStudents if already loaded, otherwise fetch all
      let allData = allStudents;
      if (allStudents.length === 0) {
        allData = await fetchAllStudents();
      }
      
      // Apply filters to get the filtered dataset
      let filteredData = allData;
      
      if (selectedDepartment) {
        filteredData = filteredData.filter(student => student.department === selectedDepartment);
      }
      
      if (selectedYear !== 'all') {
        filteredData = filteredData.filter(student => student.year === selectedYear);
      }
      
      if (debouncedSearchTerm) {
        const searchLower = debouncedSearchTerm.toLowerCase();
        filteredData = filteredData.filter(student => 
          student.name.toLowerCase().includes(searchLower) ||
          student.rollNumber.toLowerCase().includes(searchLower)
        );
      }

      // Implement client-side pagination
      const studentsPerPage = 10;
      const startIndex = (page - 1) * studentsPerPage;
      const endIndex = startIndex + studentsPerPage;
      const paginatedStudents = filteredData.slice(startIndex, endIndex);
      
      setStudents(paginatedStudents);
      setCurrentPage(page);
      setTotalPages(Math.ceil(filteredData.length / studentsPerPage));
      setTotalStudents(filteredData.length);
      
      setLoading(false);
    } catch (err) {
      console.error('Error fetching students:', err);
      
      if (err.response?.status === 401) {
        setError('Authentication failed. Please login again.');
      } else if (err.response?.status === 403) {
        setError('You do not have permission to view students. Admin access required.');
      } else if (err.message.includes('token')) {
        setError('Please login to access student management.');
      } else {
        setError(`Error: ${err.message}`);
      }
      
      setStudents([]);
      setLoading(false);
    }
  };

  // Helper function to determine year from branch (you can customize this logic)
  const getYearFromBranch = (branch, student) => {
    if (student && student.joining_year && student.passout_year) {
      return `${student.joining_year}-${student.passout_year}`;
    }
    return 'N/A';
  };

  // Initial data fetch
  useEffect(() => {
    fetchStudents();
  }, []);

  // Add this useEffect after your existing useEffect
  useEffect(() => {
    // Check if user is authenticated
    const token = getAuthToken();
    if (!token) {
      // Redirect to login page or show login prompt
      setError('Please login to access student management.');
      setLoading(false);
      return;
    }
    
    fetchStudents();
  }, []);

  // Helper function to extract year from student ID (assuming format like *********)
  const getYearFromStudentId = (studentId) => {
    if (studentId && studentId.length >= 6) {
      const yearPart = studentId.substring(2, 6);
      if (!isNaN(yearPart)) {
        return `${4 - (new Date().getFullYear() - parseInt(yearPart))}th Year`;
      }
    }
    return 'Unknown';
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsYearDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Get available years from students data
  const getAvailableYears = (studentsData) => {
    const years = [...new Set(studentsData.map(student => student.year).filter(year => year && year !== 'N/A'))];
    return years.sort();
  };

  // Get department statistics
  const getDepartmentStats = (studentsData) => {
    const stats = {};
    studentsData.forEach(student => {
      if (student.department && student.department !== 'N/A') {
        stats[student.department] = (stats[student.department] || 0) + 1;
      }
    });
    return Object.entries(stats).map(([department, count]) => ({ department, count }));
  };

  // Get available passout years for selected department
  const getAvailablePassoutYears = () => {
    if (!selectedDepartment) return [];
    const years = allStudents
      .filter(s => s.department === selectedDepartment && s.year && s.year !== 'N/A')
      .map(s => {
        // Extract passout year from year string (format: "joining-passout")
        const parts = s.year.split('-');
        return parts.length === 2 ? parts[1] : null;
      })
      .filter(y => y)
      .map(Number)
      .filter(y => !isNaN(y));
    // Unique and descending
    return Array.from(new Set(years)).sort((a, b) => b - a);
  };

  // Filter students for selected department and passout year
  const getFilteredStudents = () => {
    let filtered = allStudents;
    if (selectedDepartment) {
      filtered = filtered.filter(s => s.department === selectedDepartment);
    }
    if (selectedPassoutYear) {
      filtered = filtered.filter(s => {
        if (!s.year || s.year === 'N/A') return false;
        const parts = s.year.split('-');
        return parts.length === 2 && parts[1] === String(selectedPassoutYear);
      });
    }
    if (debouncedSearchTerm) {
      const searchLower = debouncedSearchTerm.toLowerCase();
      filtered = filtered.filter(student => 
        student.name.toLowerCase().includes(searchLower) ||
        student.rollNumber.toLowerCase().includes(searchLower)
      );
    }
    // CGPA filter
    filtered = filtered.filter(student => {
      const cgpa = parseFloat(student.cgpa);
      if (cgpaMin && (isNaN(cgpa) || cgpa < parseFloat(cgpaMin))) return false;
      if (cgpaMax && (isNaN(cgpa) || cgpa > parseFloat(cgpaMax))) return false;
      return true;
    });
    return filtered;
  };

  // Update filters and refetch when dependencies change (but not searchTerm)
  useEffect(() => {
    if (allStudents.length > 0) {
      fetchStudents(1); // Reset to page 1 when filters change
    }
  }, [selectedDepartment, selectedYear, debouncedSearchTerm, selectedPassoutYear]);

  // Filter students based on selected department, year, and search term
  const filteredStudents = students; // Students are already filtered in fetchStudents

  const handleStudentClick = (student) => {
    setSelectedStudent(student);
    setEditedStudent({ ...student });
    setIsEditing(false);
  };

  const handleBackToList = () => {
    setSelectedStudent(null);
    setIsEditing(false);
    setEditedStudent(null);
  };

  const handleBackToDepartments = () => {
    setSelectedDepartment(null);
    setSelectedYear('all');
    setSearchTerm('');
  };

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSave = () => {
    // Update the student in the list
    setStudents(prev => 
      prev.map(student => 
        student.id === editedStudent.id ? editedStudent : student
      )
    );
    setSelectedStudent(editedStudent);
    setIsEditing(false);
    // Here you would typically make an API call to save the changes
  };

  const handleCancel = () => {
    setEditedStudent({ ...selectedStudent });
    setIsEditing(false);
  };

  const handleInputChange = (field, value) => {
    setEditedStudent(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle retry button click
  const handleRetry = () => {
    setIsRetrying(true);
    fetchStudents();
  };

  // Help developers find the correct API endpoint
  const debugBackend = () => {
    window.open('http://localhost:8000/admin/');
  };

  const handleSearch = () => {
    // Force immediate search without waiting for debounce
    setDebouncedSearchTerm(searchTerm);
    setCurrentPage(1);
  };

  // Handle pagination
  const handlePageChange = (newPage) => {
    if (newPage >= 1 && newPage <= totalPages) {
      fetchStudents(newPage);
    }
  };

  // Handle search input change
  const handleSearchInputChange = (e) => {
    setSearchTerm(e.target.value);
    // Don't trigger immediate search, let debounce handle it
  };

  // Handle search input key press
  const handleSearchKeyDown = (e) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleSearch();
    }
  };

  if (loading) return (
    <div className="flex-1 p-6 ml-20 overflow-y-auto h-full">
      <div className="flex flex-col items-center justify-center h-full">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mb-4"></div>
        <div className="text-gray-600">Loading students...</div>
      </div>
    </div>
  );
  
  if (error && students.length === 0) return (
    <div className="flex-1 p-6 ml-20 overflow-y-auto h-full">
      <div className="flex flex-col items-center justify-center h-full">
        <div className="text-red-500 mb-4 text-center max-w-md">
          <p className="font-semibold text-lg mb-2">Access Error</p>
          <p>{error}</p>
          <div className="mt-4 text-sm text-gray-600">
            <p>Possible solutions:</p>
            <ul className="list-disc list-inside mt-2 text-left">
              <li>Make sure you're logged in with admin credentials</li>
              <li>Check if your session has expired</li>
              <li>Verify Django server is running on port 8000</li>
              <li>Ensure proper permissions are set in Django</li>
            </ul>
          </div>
        </div>
        <div className="flex gap-3 mt-4">
          {!error.includes('login') && (
            <button 
              onClick={handleRetry}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              disabled={isRetrying}
            >
              <RefreshCw className={`w-4 h-4 ${isRetrying ? 'animate-spin' : ''}`} />
              {isRetrying ? 'Retrying...' : 'Retry'}
            </button>
          )}
          <button 
            onClick={() => window.location.href = '/login'}
            className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
          >
            <User className="w-4 h-4" />
            Go to Login
          </button>
        </div>
      </div>
    </div>
  );

  return (
    <div className="flex-1 p-6 ml-20 overflow-y-auto h-full">
      {!selectedStudent ? (
        <>
          {!selectedDepartment ? (
            <DepartmentCards
              departmentOptions={departmentOptions}
              departmentStats={departmentStats}
              allStudents={allStudents}
              onSelect={setSelectedDepartment}
            />
          ) : !selectedPassoutYear ? (
            <PassoutYearCards
              departmentLabel={departmentOptions.find(d => d.value === selectedDepartment)?.label}
              onBack={handleBackToDepartments}
              getAvailablePassoutYears={getAvailablePassoutYears}
              allStudents={allStudents}
              selectedDepartment={selectedDepartment}
              onSelectYear={setSelectedPassoutYear}
            />
          ) : (
            <StudentList
              departmentLabel={departmentOptions.find(d => d.value === selectedDepartment)?.label}
              passoutYear={selectedPassoutYear}
              onBack={() => setSelectedPassoutYear(null)}
              searchTerm={searchTerm}
              handleSearchInputChange={handleSearchInputChange}
              handleSearchKeyDown={handleSearchKeyDown}
              cgpaMin={cgpaMin}
              setCgpaMin={setCgpaMin}
              cgpaMax={cgpaMax}
              setCgpaMax={setCgpaMax}
              handleSearch={handleSearch}
              getFilteredStudents={getFilteredStudents}
              currentPage={currentPage}
              handlePageChange={handlePageChange}
              handleStudentClick={handleStudentClick}
              loading={loading}
            />
          )}
        </>
      ) : (
        <StudentProfile
          selectedStudent={selectedStudent}
          editedStudent={editedStudent}
          isEditing={isEditing}
          handleBackToList={handleBackToList}
          handleEdit={handleEdit}
          handleSave={handleSave}
          handleCancel={handleCancel}
          handleInputChange={handleInputChange}
          departmentOptions={departmentOptions}
        />
      )}
    </div>
  );
}