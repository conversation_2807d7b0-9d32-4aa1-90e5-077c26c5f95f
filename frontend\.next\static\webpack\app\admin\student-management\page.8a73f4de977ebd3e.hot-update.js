"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/student-management/page",{

/***/ "(app-pages-browser)/./src/app/admin/student-management/StudentProfile.jsx":
/*!*************************************************************!*\
  !*** ./src/app/admin/student-management/StudentProfile.jsx ***!
  \*************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StudentProfile)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Edit_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Edit,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Edit_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Edit,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Edit_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Edit,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Edit_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Edit,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_FaBuilding_FaFileAlt_FaMapMarkerAlt_FaPhoneAlt_FaSpinner_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=FaBuilding,FaFileAlt,FaMapMarkerAlt,FaPhoneAlt,FaSpinner,FaUser!=!react-icons/fa */ \"(app-pages-browser)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var _api_students__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../api/students */ \"(app-pages-browser)/./src/api/students.js\");\n/* harmony import */ var _DocumentsModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./DocumentsModal */ \"(app-pages-browser)/./src/app/admin/student-management/DocumentsModal.jsx\");\n/* harmony import */ var _ResumeModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ResumeModal */ \"(app-pages-browser)/./src/app/admin/student-management/ResumeModal.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction StudentProfile(param) {\n    let { selectedStudent, editedStudent, isEditing, handleBackToList, handleEdit, handleSave, handleCancel, handleInputChange, departmentOptions } = param;\n    _s();\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [semesterMarksheets, setSemesterMarksheets] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isDocumentsModalOpen, setIsDocumentsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isResumeModalOpen, setIsResumeModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [companyStats, setCompanyStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        loading: false,\n        totalListings: 0,\n        eligibleJobs: 0\n    });\n    const [resumeCount, setResumeCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [lastResumeUpdate, setLastResumeUpdate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Use selectedStudent as the profile data, but allow fetching more details if needed\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentProfile.useEffect\": ()=>{\n            if (selectedStudent) {\n                setProfile(selectedStudent);\n                // Optionally fetch additional details if needed\n                fetchAdditionalDetails(selectedStudent.id);\n                // Fetch company statistics\n                fetchCompanyStats(selectedStudent.id);\n                // Fetch resume information\n                fetchResumeInfo(selectedStudent.id);\n            }\n        }\n    }[\"StudentProfile.useEffect\"], [\n        selectedStudent\n    ]);\n    // Fetch additional details if needed\n    const fetchAdditionalDetails = async (studentId)=>{\n        if (!studentId) return;\n        try {\n            setLoading(true);\n            // This could fetch semester marksheets or other detailed info not included in the list view\n            // const details = await studentsAPI.getStudentDetails(studentId);\n            // setSemesterMarksheets(details.semesterMarksheets || []);\n            setLoading(false);\n        } catch (err) {\n            console.error('Error fetching student details:', err);\n            setLoading(false);\n        }\n    };\n    // Fetch company statistics for the student\n    const fetchCompanyStats = async (studentId)=>{\n        if (!studentId) return;\n        try {\n            setCompanyStats((prev)=>({\n                    ...prev,\n                    loading: true\n                }));\n            // Mock implementation - replace with actual API call\n            // const stats = await studentsAPI.getStudentCompanyStats(studentId);\n            // For now, provide mock data\n            const mockStats = {\n                loading: false,\n                totalListings: 25,\n                eligibleJobs: 18\n            };\n            setCompanyStats(mockStats);\n        } catch (err) {\n            console.error('Error fetching company stats:', err);\n            setCompanyStats({\n                loading: false,\n                totalListings: 0,\n                eligibleJobs: 0\n            });\n        }\n    };\n    // Fetch resume information for the student\n    const fetchResumeInfo = async (studentId)=>{\n        if (!studentId) return;\n        try {\n            // Mock implementation - replace with actual API call\n            // const resumeInfo = await studentsAPI.getStudentResumes(studentId);\n            // For now, provide mock data\n            const mockResumeCount = 2;\n            const mockLastUpdate = new Date('2024-01-15').toISOString();\n            setResumeCount(mockResumeCount);\n            setLastResumeUpdate(mockLastUpdate);\n        } catch (err) {\n            console.error('Error fetching resume info:', err);\n            setResumeCount(0);\n            setLastResumeUpdate(null);\n        }\n    };\n    // Calculate overall CGPA\n    const calculateOverallCGPA = ()=>{\n        return (profile === null || profile === void 0 ? void 0 : profile.cgpa) || 'N/A';\n    };\n    // Calculate percentage from CGPA (approximation)\n    const calculatePercentage = (cgpa)=>{\n        if (!cgpa || cgpa === 'N/A') return '';\n        const numericCgpa = parseFloat(cgpa);\n        if (isNaN(numericCgpa)) return '';\n        return (numericCgpa * 9.5).toFixed(2) + '%';\n    };\n    // Format year range if available\n    const formatEducationPeriod = (joiningYear, passoutYear)=>{\n        if (joiningYear && passoutYear) {\n            return \"\".concat(joiningYear, \" - \").concat(passoutYear);\n        }\n        return 'N/A';\n    };\n    // Get year range for student\n    const getYearRange = ()=>{\n        if ((profile === null || profile === void 0 ? void 0 : profile.joining_year) && (profile === null || profile === void 0 ? void 0 : profile.passout_year)) {\n            return \"\".concat(profile.joining_year, \" - \").concat(profile.passout_year);\n        }\n        if ((editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.joining_year) && (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.passout_year)) {\n            return \"\".concat(editedStudent.joining_year, \" - \").concat(editedStudent.passout_year);\n        }\n        if ((profile === null || profile === void 0 ? void 0 : profile.year) || (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.year)) {\n            return (profile === null || profile === void 0 ? void 0 : profile.year) || (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.year);\n        }\n        return null;\n    };\n    // Get semester CGPA (mock implementation)\n    const getSemesterCGPA = (semester)=>{\n        var _semesterMarksheets_;\n        return ((_semesterMarksheets_ = semesterMarksheets[semester - 1]) === null || _semesterMarksheets_ === void 0 ? void 0 : _semesterMarksheets_.cgpa) || '-';\n    };\n    // Handle resume upload\n    const handleResumeUpload = async (file)=>{\n        try {\n            await _api_students__WEBPACK_IMPORTED_MODULE_3__.studentsAPI.uploadResume(file);\n            // Refresh resume info after upload\n            if (selectedStudent) {\n                fetchResumeInfo(selectedStudent.id);\n            }\n        } catch (err) {\n            console.error('Error uploading resume:', err);\n            alert('Failed to upload resume. Please try again.');\n        }\n    };\n    // Handle resume delete\n    const handleResumeDelete = async (resumeId)=>{\n        try {\n            await _api_students__WEBPACK_IMPORTED_MODULE_3__.studentsAPI.deleteResume(resumeId);\n            // Refresh resume info after delete\n            if (selectedStudent) {\n                fetchResumeInfo(selectedStudent.id);\n            }\n        } catch (err) {\n            console.error('Error deleting resume:', err);\n            alert('Failed to delete resume. Please try again.');\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBuilding_FaFileAlt_FaMapMarkerAlt_FaPhoneAlt_FaSpinner_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_6__.FaSpinner, {\n                    className: \"animate-spin text-blue-500 text-xl mr-2\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                    lineNumber: 187,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: \"Loading details...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                    lineNumber: 188,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n            lineNumber: 186,\n            columnNumber: 7\n        }, this);\n    }\n    if (!selectedStudent && !editedStudent) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center p-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-500\",\n                    children: \"No student selected\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                    lineNumber: 196,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleBackToList,\n                    className: \"mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                    children: \"Back to List\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                    lineNumber: 197,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n            lineNumber: 195,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleBackToList,\n                        className: \"flex items-center gap-2 text-blue-600 hover:text-blue-800 transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Edit_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                lineNumber: 215,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Back to List\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                lineNumber: 216,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleSave,\n                                    className: \"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors mr-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Edit_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Save\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleCancel,\n                                    className: \"px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Edit_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleEdit,\n                            className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Edit_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Edit\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                lineNumber: 246,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                            lineNumber: 242,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                        lineNumber: 219,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                lineNumber: 210,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-xl shadow-sm overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-blue-50 p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-16 h-16 bg-blue-500 text-white flex items-center justify-center rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.name) ? editedStudent.name[0].toUpperCase() : 'S'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-grow\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-xl font-semibold text-gray-800\",\n                                            children: isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.name) || '',\n                                                onChange: (e)=>handleInputChange('name', e.target.value),\n                                                className: \"w-full p-1 border rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 19\n                                            }, this) : (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.name) || 'Unknown Student'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1 text-gray-600\",\n                                            children: [\n                                                isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.rollNumber) || '',\n                                                    onChange: (e)=>handleInputChange('rollNumber', e.target.value),\n                                                    className: \"p-1 border rounded mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 19\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        \"ID: \",\n                                                        (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.rollNumber) || 'N/A'\n                                                    ]\n                                                }, void 0, true),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mx-2\",\n                                                    children: \"|\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 17\n                                                }, this),\n                                                isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.department) || '',\n                                                    onChange: (e)=>handleInputChange('department', e.target.value),\n                                                    className: \"p-1 border rounded mr-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"\",\n                                                            children: \"Select Department\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        departmentOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: option.value,\n                                                                children: option.label\n                                                            }, option.value, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                lineNumber: 303,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 19\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        \"Department: \",\n                                                        (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.department) || 'N/A'\n                                                    ]\n                                                }, void 0, true),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mx-2\",\n                                                    children: \"|\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 17\n                                                }, this),\n                                                isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.year) || '',\n                                                    onChange: (e)=>handleInputChange('year', e.target.value),\n                                                    className: \"p-1 border rounded\",\n                                                    placeholder: \"YYYY-YYYY\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 19\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        \"Year: \",\n                                                        getYearRange() || 'N/A'\n                                                    ]\n                                                }, void 0, true)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-100 px-3 py-1 rounded-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-blue-600 font-medium\",\n                                                children: [\n                                                    \"CGPA: \",\n                                                    calculateOverallCGPA()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 17\n                                            }, this),\n                                            calculateOverallCGPA() !== 'N/A' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500 ml-1\",\n                                                children: [\n                                                    \"(\",\n                                                    calculatePercentage(calculateOverallCGPA()),\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                            lineNumber: 259,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                        lineNumber: 258,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-medium mb-4 text-gray-800 border-b pb-2\",\n                                            children: \"Personal Information\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 344,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-gray-600 text-sm mb-1\",\n                                                            children: \"Email\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 348,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"email\",\n                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.email) || '',\n                                                            onChange: (e)=>handleInputChange('email', e.target.value),\n                                                            className: \"w-full p-2 border rounded-lg\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-800\",\n                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.email) || 'N/A'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 357,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-gray-600 text-sm mb-1\",\n                                                            children: \"Phone\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 362,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"tel\",\n                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.phone) || '',\n                                                            onChange: (e)=>handleInputChange('phone', e.target.value),\n                                                            className: \"w-full p-2 border rounded-lg\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 364,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-800\",\n                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.phone) || 'N/A'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 371,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-gray-600 text-sm mb-1\",\n                                                            children: \"Date of Birth\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"date\",\n                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.dateOfBirth) || '',\n                                                            onChange: (e)=>handleInputChange('dateOfBirth', e.target.value),\n                                                            className: \"w-full p-2 border rounded-lg\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 378,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-800\",\n                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.dateOfBirth) || 'N/A'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 385,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-gray-600 text-sm mb-1\",\n                                                            children: \"Parent Contact\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 390,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"tel\",\n                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.parentContact) || '',\n                                                            onChange: (e)=>handleInputChange('parentContact', e.target.value),\n                                                            className: \"w-full p-2 border rounded-lg\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 392,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-800\",\n                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.parentContact) || 'N/A'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 399,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                    lineNumber: 343,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-medium mb-4 text-gray-800 border-b pb-2\",\n                                            children: \"Address & Education\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-gray-600 text-sm mb-1\",\n                                                            children: \"Address\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 411,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.address) || '',\n                                                            onChange: (e)=>handleInputChange('address', e.target.value),\n                                                            className: \"w-full p-2 border rounded-lg\",\n                                                            rows: 2\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 413,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-800\",\n                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.address) || 'N/A'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 420,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 410,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-gray-600 text-sm mb-1\",\n                                                            children: \"Education\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 425,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.education) || '',\n                                                            onChange: (e)=>handleInputChange('education', e.target.value),\n                                                            className: \"w-full p-2 border rounded-lg\",\n                                                            rows: 2\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 427,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-800\",\n                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.education) || 'N/A'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 434,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                    lineNumber: 406,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"md:col-span-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-medium mb-4 text-gray-800 border-b pb-2\",\n                                            children: \"Skills\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 442,\n                                            columnNumber: 15\n                                        }, this),\n                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: Array.isArray(editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.skills) ? editedStudent.skills.join(', ') : (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.skills) || '',\n                                            onChange: (e)=>handleInputChange('skills', e.target.value.split(',').map((s)=>s.trim())),\n                                            className: \"w-full p-2 border rounded-lg\",\n                                            rows: 2,\n                                            placeholder: \"Enter skills separated by commas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 445,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: Array.isArray(editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.skills) && editedStudent.skills.length > 0 ? editedStudent.skills.map((skill, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm\",\n                                                    children: skill\n                                                }, index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 456,\n                                                    columnNumber: 23\n                                                }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"No skills listed\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 464,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 453,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                    lineNumber: 441,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                            lineNumber: 341,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                        lineNumber: 340,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-6 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg p-5 shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-6 text-gray-800\",\n                                        children: \"Academic\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-800\",\n                                                        children: \"Semester Wise score\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-blue-50 inline-flex items-center px-3 py-1 rounded-full ml-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-blue-600 font-medium\",\n                                                                children: calculateOverallCGPA()\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                lineNumber: 481,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-500 ml-1\",\n                                                                children: \"CGPA\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                lineNumber: 482,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-blue-600 ml-2\",\n                                                                children: calculatePercentage(calculateOverallCGPA())\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                lineNumber: 483,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 480,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 478,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-lg font-semibold text-gray-800\",\n                                                children: isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.joining_year) || '',\n                                                            onChange: (e)=>handleInputChange('joining_year', e.target.value),\n                                                            className: \"w-20 p-1 border rounded text-sm\",\n                                                            placeholder: \"2020\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 489,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"-\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 496,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.passout_year) || '',\n                                                            onChange: (e)=>handleInputChange('passout_year', e.target.value),\n                                                            className: \"w-20 p-1 border rounded text-sm\",\n                                                            placeholder: \"2024\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 497,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 488,\n                                                    columnNumber: 21\n                                                }, this) : formatEducationPeriod(editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.joining_year, editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.passout_year)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 486,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 477,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"overflow-x-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                className: \"w-full border-collapse border border-gray-300\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            className: \"bg-gray-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"border border-gray-300 px-4 py-3 text-sm font-semibold text-gray-700\",\n                                                                    children: \"Sem\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                    lineNumber: 516,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                [\n                                                                    1,\n                                                                    2,\n                                                                    3,\n                                                                    4,\n                                                                    5,\n                                                                    6,\n                                                                    7,\n                                                                    8\n                                                                ].map((sem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"border border-gray-300 px-4 py-3 text-sm font-semibold text-gray-700\",\n                                                                        children: sem\n                                                                    }, sem, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                        lineNumber: 518,\n                                                                        columnNumber: 25\n                                                                    }, this))\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 515,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 514,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"border border-gray-300 px-4 py-3 text-sm font-medium text-gray-700\",\n                                                                    children: \"Cgpa\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                    lineNumber: 524,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                [\n                                                                    1,\n                                                                    2,\n                                                                    3,\n                                                                    4,\n                                                                    5,\n                                                                    6,\n                                                                    7,\n                                                                    8\n                                                                ].map((sem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"border border-gray-300 px-4 py-3 text-sm text-gray-700\",\n                                                                        children: getSemesterCGPA(sem)\n                                                                    }, sem, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                        lineNumber: 526,\n                                                                        columnNumber: 25\n                                                                    }, this))\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 523,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 522,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 513,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 512,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 511,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                        className: \"my-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 535,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-gray-800\",\n                                                                children: \"Class XII\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                lineNumber: 541,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-blue-50 inline-flex items-center px-3 py-1 rounded-full ml-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-blue-600 font-medium\",\n                                                                        children: (profile === null || profile === void 0 ? void 0 : profile.twelfth_cgpa) || '-'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                        lineNumber: 543,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-gray-500 ml-1\",\n                                                                        children: \"CGPA\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                        lineNumber: 544,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-blue-600 ml-2\",\n                                                                        children: (profile === null || profile === void 0 ? void 0 : profile.twelfth_percentage) || '-'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                        lineNumber: 545,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                lineNumber: 542,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 540,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-lg font-semibold text-gray-800\",\n                                                        children: (profile === null || profile === void 0 ? void 0 : profile.twelfth_year_of_passing) ? \"\".concat(parseInt(profile.twelfth_year_of_passing) - 2, \" - \").concat(profile.twelfth_year_of_passing) : '-'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 548,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 539,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start mb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-6 w-full\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-500 text-sm w-[120px]\",\n                                                                            children: \"College :\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 558,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-700 font-medium\",\n                                                                            children: (profile === null || profile === void 0 ? void 0 : profile.twelfth_school) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 559,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                    lineNumber: 557,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-500 text-sm w-[120px]\",\n                                                                            children: \"Board :\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 562,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-700 font-medium\",\n                                                                            children: (profile === null || profile === void 0 ? void 0 : profile.twelfth_board) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 563,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                    lineNumber: 561,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 556,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-500 text-sm w-[120px]\",\n                                                                            children: \"Location :\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 568,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-700 font-medium\",\n                                                                            children: (profile === null || profile === void 0 ? void 0 : profile.city) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 569,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                    lineNumber: 567,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-500 text-sm w-[120px]\",\n                                                                            children: \"Specialization :\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 572,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-700 font-medium\",\n                                                                            children: (profile === null || profile === void 0 ? void 0 : profile.twelfth_specialization) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 573,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                    lineNumber: 571,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 566,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 555,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 554,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 538,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                        className: \"my-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 584,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-gray-800\",\n                                                                children: \"Class X\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                lineNumber: 590,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-blue-50 inline-flex items-center px-3 py-1 rounded-full ml-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-blue-600 font-medium\",\n                                                                        children: (profile === null || profile === void 0 ? void 0 : profile.tenth_cgpa) || '-'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                        lineNumber: 592,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-gray-500 ml-1\",\n                                                                        children: \"CGPA\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                        lineNumber: 593,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-blue-600 ml-2\",\n                                                                        children: (profile === null || profile === void 0 ? void 0 : profile.tenth_percentage) || '-'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                        lineNumber: 594,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                lineNumber: 591,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 589,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-lg font-semibold text-gray-800\",\n                                                        children: (profile === null || profile === void 0 ? void 0 : profile.tenth_year_of_passing) ? \"\".concat(parseInt(profile.tenth_year_of_passing) - 1, \" - \").concat(profile.tenth_year_of_passing) : '-'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 597,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 588,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start mb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-6 w-full\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-500 text-sm w-[120px]\",\n                                                                            children: \"School :\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 608,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-700 font-medium\",\n                                                                            children: (profile === null || profile === void 0 ? void 0 : profile.tenth_school) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 609,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                    lineNumber: 607,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-500 text-sm w-[120px]\",\n                                                                            children: \"Board :\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 612,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-700 font-medium\",\n                                                                            children: (profile === null || profile === void 0 ? void 0 : profile.tenth_board) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 613,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                    lineNumber: 611,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 606,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-500 text-sm w-[120px]\",\n                                                                            children: \"Location :\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 618,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-700 font-medium\",\n                                                                            children: (profile === null || profile === void 0 ? void 0 : profile.city) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 619,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                    lineNumber: 617,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-500 text-sm w-[120px]\",\n                                                                            children: \"Specialization :\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 622,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-700 font-medium\",\n                                                                            children: \"-\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 623,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                    lineNumber: 621,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 616,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 605,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 604,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 587,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                lineNumber: 475,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg p-5 shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-4 text-gray-800\",\n                                        children: \"Companies\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 633,\n                                        columnNumber: 13\n                                    }, this),\n                                    companyStats.loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center py-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBuilding_FaFileAlt_FaMapMarkerAlt_FaPhoneAlt_FaSpinner_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_6__.FaSpinner, {\n                                                className: \"animate-spin text-blue-500 text-xl mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 637,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Loading company data...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 638,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 636,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-500 text-sm\",\n                                                            children: \"Total Listings\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 645,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-semibold text-gray-700\",\n                                                            children: companyStats.totalListings\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 646,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 644,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 643,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-500 text-sm\",\n                                                            children: \"Eligible Jobs\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 653,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-semibold text-gray-700\",\n                                                            children: companyStats.eligibleJobs\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 654,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 652,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 651,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                lineNumber: 632,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                        lineNumber: 473,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-3 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg p-5 shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-4 text-gray-800\",\n                                        children: \"My Files\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 666,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center p-2 mb-3 cursor-pointer hover:bg-gray-50 rounded-lg transition-colors\",\n                                        onClick: ()=>setIsResumeModalOpen(true),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-blue-600\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"h-6 w-6\",\n                                                        fill: \"none\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        stroke: \"currentColor\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 675,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 674,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 673,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 672,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-grow\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-medium text-gray-700\",\n                                                        children: \"Resumes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 680,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: resumeCount > 0 ? \"\".concat(resumeCount, \" resume\").concat(resumeCount > 1 ? 's' : '', \" uploaded\") + (lastResumeUpdate ? \" • Last updated \".concat(new Date(lastResumeUpdate).toLocaleDateString()) : '') : 'No resumes uploaded'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 681,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 679,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-50 px-3 py-1 rounded-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-600 font-medium\",\n                                                    children: resumeCount\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 690,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 689,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 668,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center p-2 cursor-pointer hover:bg-gray-50 rounded-lg transition-colors\",\n                                        onClick: ()=>setIsDocumentsModalOpen(true),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-blue-600\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"h-6 w-6\",\n                                                        fill: \"none\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        stroke: \"currentColor\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 701,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 700,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 699,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 698,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-grow\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-medium text-gray-700\",\n                                                        children: \"Documents\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 706,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Academic certificates and marksheets\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 707,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 705,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-50 px-3 py-1 rounded-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-600 font-medium\",\n                                                    children: ((profile === null || profile === void 0 ? void 0 : profile.tenth_certificate) ? 1 : 0) + ((profile === null || profile === void 0 ? void 0 : profile.twelfth_certificate) ? 1 : 0) + semesterMarksheets.length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 710,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 709,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 694,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                lineNumber: 665,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg p-5 shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold text-gray-800\",\n                                            children: \"CURRENT ADDRESS\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 722,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 721,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500 w-20\",\n                                                        children: \"City\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 727,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-700\",\n                                                        children: [\n                                                            \": \",\n                                                            (profile === null || profile === void 0 ? void 0 : profile.city) || '-'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 728,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 726,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500 w-20\",\n                                                        children: \"District\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 731,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-700\",\n                                                        children: [\n                                                            \": \",\n                                                            (profile === null || profile === void 0 ? void 0 : profile.district) || '-'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 732,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 730,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500 w-20\",\n                                                        children: \"State\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 735,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-700\",\n                                                        children: [\n                                                            \": \",\n                                                            (profile === null || profile === void 0 ? void 0 : profile.state) || '-'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 736,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 734,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500 w-20\",\n                                                        children: \"Pin Code\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 739,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-700\",\n                                                        children: [\n                                                            \": \",\n                                                            (profile === null || profile === void 0 ? void 0 : profile.pincode) || '-'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 740,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 738,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500 w-20\",\n                                                        children: \"Country\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 743,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-700\",\n                                                        children: [\n                                                            \": \",\n                                                            (profile === null || profile === void 0 ? void 0 : profile.country) || '-'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 744,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 742,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500 w-20\",\n                                                        children: \"Address\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 747,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-700\",\n                                                        children: [\n                                                            \": \",\n                                                            (profile === null || profile === void 0 ? void 0 : profile.address) || '-'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 748,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 746,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 725,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                lineNumber: 720,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                        lineNumber: 663,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ResumeModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        isOpen: isResumeModalOpen,\n                        onClose: ()=>setIsResumeModalOpen(false),\n                        resume: (profile === null || profile === void 0 ? void 0 : profile.resume_url) || (profile === null || profile === void 0 ? void 0 : profile.resume),\n                        onUpload: handleResumeUpload,\n                        onDelete: handleResumeDelete\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                        lineNumber: 756,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DocumentsModal__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        isOpen: isDocumentsModalOpen,\n                        onClose: ()=>setIsDocumentsModalOpen(false),\n                        documents: {\n                            tenth: (profile === null || profile === void 0 ? void 0 : profile.tenth_certificate_url) || (profile === null || profile === void 0 ? void 0 : profile.tenth_certificate),\n                            twelfth: (profile === null || profile === void 0 ? void 0 : profile.twelfth_certificate_url) || (profile === null || profile === void 0 ? void 0 : profile.twelfth_certificate),\n                            semesterMarksheets: semesterMarksheets\n                        },\n                        onUploadCertificate: _api_students__WEBPACK_IMPORTED_MODULE_3__.studentsAPI.uploadCertificate,\n                        onUploadMarksheet: _api_students__WEBPACK_IMPORTED_MODULE_3__.studentsAPI.uploadSemesterMarksheet\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                        lineNumber: 765,\n                        columnNumber: 7\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                lineNumber: 256,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n        lineNumber: 208,\n        columnNumber: 5\n    }, this);\n}\n_s(StudentProfile, \"ErkQrXcp6uhP342rBjEs2qSg9w8=\");\n_c = StudentProfile;\nvar _c;\n$RefreshReg$(_c, \"StudentProfile\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/student-management/StudentProfile.jsx\n"));

/***/ })

});