"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/student-management/page",{

/***/ "(app-pages-browser)/./src/app/admin/student-management/page.jsx":
/*!***************************************************!*\
  !*** ./src/app/admin/student-management/page.jsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StudentManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_GraduationCap_RefreshCw_Save_Search_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,GraduationCap,RefreshCw,Save,Search,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_GraduationCap_RefreshCw_Save_Search_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,GraduationCap,RefreshCw,Save,Search,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _api_students__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../api/students */ \"(app-pages-browser)/./src/api/students.js\");\n/* harmony import */ var _api_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../api/auth */ \"(app-pages-browser)/./src/api/auth.js\");\n/* harmony import */ var _StudentDropdown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./StudentDropdown */ \"(app-pages-browser)/./src/app/admin/student-management/StudentDropdown.jsx\");\n/* harmony import */ var _StudentProfile__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./StudentProfile */ \"(app-pages-browser)/./src/app/admin/student-management/StudentProfile.jsx\");\n/* harmony import */ var _DepartmentCards__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./DepartmentCards */ \"(app-pages-browser)/./src/app/admin/student-management/DepartmentCards.jsx\");\n/* harmony import */ var _PassoutYearCards__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./PassoutYearCards */ \"(app-pages-browser)/./src/app/admin/student-management/PassoutYearCards.jsx\");\n/* harmony import */ var _StudentList__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./StudentList */ \"(app-pages-browser)/./src/app/admin/student-management/StudentList.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction StudentManagement() {\n    var _departmentOptions_find, _departmentOptions_find1;\n    _s();\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [debouncedSearchTerm, setDebouncedSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedDepartment, setSelectedDepartment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedYear, setSelectedYear] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [selectedStudent, setSelectedStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isEditing, setIsEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editedStudent, setEditedStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isYearDropdownOpen, setIsYearDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allStudents, setAllStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isRetrying, setIsRetrying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalStudents, setTotalStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [availableYears, setAvailableYears] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [departmentStats, setDepartmentStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedPassoutYear, setSelectedPassoutYear] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [cgpaMin, setCgpaMin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [cgpaMax, setCgpaMax] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Dropdown options\n    const departmentOptions = [\n        {\n            value: 'Computer Science',\n            label: 'Computer Science'\n        },\n        {\n            value: 'Electronics',\n            label: 'Electronics'\n        },\n        {\n            value: 'Mechanical',\n            label: 'Mechanical'\n        },\n        {\n            value: 'Civil',\n            label: 'Civil'\n        },\n        {\n            value: 'Electrical',\n            label: 'Electrical'\n        },\n        {\n            value: 'Information Technology',\n            label: 'Information Technology'\n        },\n        {\n            value: 'Chemical',\n            label: 'Chemical'\n        },\n        {\n            value: 'Biotechnology',\n            label: 'Biotechnology'\n        }\n    ];\n    // Fetch all students for complete dataset\n    const fetchAllStudents = async ()=>{\n        try {\n            const token = (0,_api_auth__WEBPACK_IMPORTED_MODULE_3__.getAuthToken)();\n            if (!token) {\n                throw new Error('No authentication token found. Please login first.');\n            }\n            let allData = [];\n            let page = 1;\n            let hasMore = true;\n            while(hasMore){\n                const response = await _api_students__WEBPACK_IMPORTED_MODULE_2__.studentsAPI.getStudents({\n                    page,\n                    page_size: 100\n                });\n                allData = [\n                    ...allData,\n                    ...response.data\n                ];\n                if (response.pagination) {\n                    hasMore = page < response.pagination.total_pages;\n                    page++;\n                } else {\n                    hasMore = false;\n                }\n            }\n            const studentsData = allData.map((student)=>({\n                    id: student.id,\n                    rollNumber: student.student_id || 'N/A',\n                    name: \"\".concat(student.first_name || '', \" \").concat(student.last_name || '').trim() || 'Unknown',\n                    email: student.contact_email || student.email || 'N/A',\n                    phone: student.phone || 'N/A',\n                    department: student.branch || 'N/A',\n                    year: getYearFromBranch(student.branch, student),\n                    cgpa: student.gpa || 'N/A',\n                    address: student.address || 'N/A',\n                    dateOfBirth: student.date_of_birth || '',\n                    parentContact: student.parent_contact || 'N/A',\n                    education: student.education || 'N/A',\n                    skills: student.skills || [],\n                    // Academic details\n                    joining_year: student.joining_year || student.admission_year || '',\n                    passout_year: student.passout_year || student.graduation_year || '',\n                    // Class XII details\n                    twelfth_cgpa: student.twelfth_cgpa || student.class_12_cgpa || '',\n                    twelfth_percentage: student.twelfth_percentage || student.class_12_percentage || '',\n                    twelfth_year_of_passing: student.twelfth_year_of_passing || student.class_12_year || '',\n                    twelfth_school: student.twelfth_school || student.class_12_school || '',\n                    twelfth_board: student.twelfth_board || student.class_12_board || '',\n                    twelfth_location: student.twelfth_location || student.class_12_location || '',\n                    twelfth_specialization: student.twelfth_specialization || student.class_12_stream || '',\n                    // Class X details\n                    tenth_cgpa: student.tenth_cgpa || student.class_10_cgpa || '',\n                    tenth_percentage: student.tenth_percentage || student.class_10_percentage || '',\n                    tenth_year_of_passing: student.tenth_year_of_passing || student.class_10_year || '',\n                    tenth_school: student.tenth_school || student.class_10_school || '',\n                    tenth_board: student.tenth_board || student.class_10_board || '',\n                    tenth_location: student.tenth_location || student.class_10_location || '',\n                    tenth_specialization: student.tenth_specialization || student.class_10_stream || '',\n                    // Address details\n                    city: student.city || '',\n                    district: student.district || '',\n                    state: student.state || '',\n                    pincode: student.pincode || student.pin_code || '',\n                    country: student.country || 'India',\n                    // Certificate URLs\n                    tenth_certificate: student.tenth_certificate || student.class_10_certificate || '',\n                    twelfth_certificate: student.twelfth_certificate || student.class_12_certificate || '',\n                    tenth_certificate_url: student.tenth_certificate_url || student.class_10_certificate_url || '',\n                    twelfth_certificate_url: student.twelfth_certificate_url || student.class_12_certificate_url || '',\n                    // Resume details\n                    resume: student.resume || '',\n                    resume_url: student.resume_url || '',\n                    // Semester-wise CGPA data\n                    semester_cgpas: student.semester_cgpas || [\n                        {\n                            semester: 1,\n                            cgpa: 8.2\n                        },\n                        {\n                            semester: 2,\n                            cgpa: 8.5\n                        },\n                        {\n                            semester: 3,\n                            cgpa: 8.7\n                        },\n                        {\n                            semester: 4,\n                            cgpa: 8.4\n                        },\n                        {\n                            semester: 5,\n                            cgpa: 8.6\n                        },\n                        {\n                            semester: 6,\n                            cgpa: 8.8\n                        },\n                        {\n                            semester: 7,\n                            cgpa: 8.3\n                        },\n                        {\n                            semester: 8,\n                            cgpa: 8.5\n                        }\n                    ],\n                    // Add some mock data if fields are empty (for testing purposes)\n                    ...!student.city && !student.twelfth_cgpa ? {\n                        city: 'Mumbai',\n                        district: 'Mumbai',\n                        state: 'Maharashtra',\n                        pincode: '400001',\n                        country: 'India',\n                        joining_year: '2020',\n                        passout_year: '2024',\n                        twelfth_cgpa: '9.2',\n                        twelfth_percentage: '87.4%',\n                        twelfth_year_of_passing: '2020',\n                        twelfth_school: 'ABC Junior College',\n                        twelfth_board: 'Maharashtra State Board',\n                        twelfth_location: 'Mumbai, Maharashtra',\n                        twelfth_specialization: 'Science',\n                        tenth_cgpa: '9.0',\n                        tenth_percentage: '85.6%',\n                        tenth_year_of_passing: '2018',\n                        tenth_school: 'XYZ High School',\n                        tenth_board: 'Maharashtra State Board',\n                        tenth_location: 'Mumbai, Maharashtra',\n                        tenth_specialization: 'General',\n                        // Mock semester-wise CGPA data\n                        semester_cgpas: [\n                            {\n                                semester: 1,\n                                cgpa: 8.2\n                            },\n                            {\n                                semester: 2,\n                                cgpa: 8.5\n                            },\n                            {\n                                semester: 3,\n                                cgpa: 8.7\n                            },\n                            {\n                                semester: 4,\n                                cgpa: 8.4\n                            },\n                            {\n                                semester: 5,\n                                cgpa: 8.6\n                            },\n                            {\n                                semester: 6,\n                                cgpa: 8.8\n                            },\n                            {\n                                semester: 7,\n                                cgpa: 8.3\n                            },\n                            {\n                                semester: 8,\n                                cgpa: 8.5\n                            }\n                        ]\n                    } : {}\n                }));\n            setAllStudents(studentsData);\n            setAvailableYears(getAvailableYears(studentsData));\n            setDepartmentStats(getDepartmentStats(studentsData));\n            return studentsData;\n        } catch (err) {\n            console.error('Error fetching all students:', err);\n            throw err;\n        }\n    };\n    // Debounce search term\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"StudentManagement.useEffect.timer\": ()=>{\n                    setDebouncedSearchTerm(searchTerm);\n                }\n            }[\"StudentManagement.useEffect.timer\"], 300); // 300ms delay\n            return ({\n                \"StudentManagement.useEffect\": ()=>clearTimeout(timer)\n            })[\"StudentManagement.useEffect\"];\n        }\n    }[\"StudentManagement.useEffect\"], [\n        searchTerm\n    ]);\n    // Fetch students from Django backend with pagination\n    const fetchStudents = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1;\n        try {\n            setLoading(true);\n            setError(null);\n            setIsRetrying(false);\n            const token = (0,_api_auth__WEBPACK_IMPORTED_MODULE_3__.getAuthToken)();\n            if (!token) {\n                throw new Error('No authentication token found. Please login first.');\n            }\n            // Use allStudents if already loaded, otherwise fetch all\n            let allData = allStudents;\n            if (allStudents.length === 0) {\n                allData = await fetchAllStudents();\n            }\n            // Apply filters to get the filtered dataset\n            let filteredData = allData;\n            if (selectedDepartment) {\n                filteredData = filteredData.filter((student)=>student.department === selectedDepartment);\n            }\n            if (selectedYear !== 'all') {\n                filteredData = filteredData.filter((student)=>student.year === selectedYear);\n            }\n            if (debouncedSearchTerm) {\n                const searchLower = debouncedSearchTerm.toLowerCase();\n                filteredData = filteredData.filter((student)=>student.name.toLowerCase().includes(searchLower) || student.rollNumber.toLowerCase().includes(searchLower));\n            }\n            // Implement client-side pagination\n            const studentsPerPage = 10;\n            const startIndex = (page - 1) * studentsPerPage;\n            const endIndex = startIndex + studentsPerPage;\n            const paginatedStudents = filteredData.slice(startIndex, endIndex);\n            setStudents(paginatedStudents);\n            setCurrentPage(page);\n            setTotalPages(Math.ceil(filteredData.length / studentsPerPage));\n            setTotalStudents(filteredData.length);\n            setLoading(false);\n        } catch (err) {\n            var _err_response, _err_response1;\n            console.error('Error fetching students:', err);\n            if (((_err_response = err.response) === null || _err_response === void 0 ? void 0 : _err_response.status) === 401) {\n                setError('Authentication failed. Please login again.');\n            } else if (((_err_response1 = err.response) === null || _err_response1 === void 0 ? void 0 : _err_response1.status) === 403) {\n                setError('You do not have permission to view students. Admin access required.');\n            } else if (err.message.includes('token')) {\n                setError('Please login to access student management.');\n            } else {\n                setError(\"Error: \".concat(err.message));\n            }\n            setStudents([]);\n            setLoading(false);\n        }\n    };\n    // Helper function to determine year from branch (you can customize this logic)\n    const getYearFromBranch = (branch, student)=>{\n        if (student && student.joining_year && student.passout_year) {\n            return \"\".concat(student.joining_year, \"-\").concat(student.passout_year);\n        }\n        return 'N/A';\n    };\n    // Initial data fetch\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            fetchStudents();\n        }\n    }[\"StudentManagement.useEffect\"], []);\n    // Add this useEffect after your existing useEffect\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            // Check if user is authenticated\n            const token = (0,_api_auth__WEBPACK_IMPORTED_MODULE_3__.getAuthToken)();\n            if (!token) {\n                // Redirect to login page or show login prompt\n                setError('Please login to access student management.');\n                setLoading(false);\n                return;\n            }\n            fetchStudents();\n        }\n    }[\"StudentManagement.useEffect\"], []);\n    // Helper function to extract year from student ID (assuming format like CS2021001)\n    const getYearFromStudentId = (studentId)=>{\n        if (studentId && studentId.length >= 6) {\n            const yearPart = studentId.substring(2, 6);\n            if (!isNaN(yearPart)) {\n                return \"\".concat(4 - (new Date().getFullYear() - parseInt(yearPart)), \"th Year\");\n            }\n        }\n        return 'Unknown';\n    };\n    // Close dropdown when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"StudentManagement.useEffect.handleClickOutside\": (event)=>{\n                    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                        setIsYearDropdownOpen(false);\n                    }\n                }\n            }[\"StudentManagement.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"StudentManagement.useEffect\": ()=>{\n                    document.removeEventListener('mousedown', handleClickOutside);\n                }\n            })[\"StudentManagement.useEffect\"];\n        }\n    }[\"StudentManagement.useEffect\"], []);\n    // Get available years from students data\n    const getAvailableYears = (studentsData)=>{\n        const years = [\n            ...new Set(studentsData.map((student)=>student.year).filter((year)=>year && year !== 'N/A'))\n        ];\n        return years.sort();\n    };\n    // Get department statistics\n    const getDepartmentStats = (studentsData)=>{\n        const stats = {};\n        studentsData.forEach((student)=>{\n            if (student.department && student.department !== 'N/A') {\n                stats[student.department] = (stats[student.department] || 0) + 1;\n            }\n        });\n        return Object.entries(stats).map((param)=>{\n            let [department, count] = param;\n            return {\n                department,\n                count\n            };\n        });\n    };\n    // Get available passout years for selected department\n    const getAvailablePassoutYears = ()=>{\n        if (!selectedDepartment) return [];\n        const years = allStudents.filter((s)=>s.department === selectedDepartment && s.year && s.year !== 'N/A').map((s)=>{\n            // Extract passout year from year string (format: \"joining-passout\")\n            const parts = s.year.split('-');\n            return parts.length === 2 ? parts[1] : null;\n        }).filter((y)=>y).map(Number).filter((y)=>!isNaN(y));\n        // Unique and descending\n        return Array.from(new Set(years)).sort((a, b)=>b - a);\n    };\n    // Filter students for selected department and passout year\n    const getFilteredStudents = ()=>{\n        let filtered = allStudents;\n        if (selectedDepartment) {\n            filtered = filtered.filter((s)=>s.department === selectedDepartment);\n        }\n        if (selectedPassoutYear) {\n            filtered = filtered.filter((s)=>{\n                if (!s.year || s.year === 'N/A') return false;\n                const parts = s.year.split('-');\n                return parts.length === 2 && parts[1] === String(selectedPassoutYear);\n            });\n        }\n        if (debouncedSearchTerm) {\n            const searchLower = debouncedSearchTerm.toLowerCase();\n            filtered = filtered.filter((student)=>student.name.toLowerCase().includes(searchLower) || student.rollNumber.toLowerCase().includes(searchLower));\n        }\n        // CGPA filter\n        filtered = filtered.filter((student)=>{\n            const cgpa = parseFloat(student.cgpa);\n            if (cgpaMin && (isNaN(cgpa) || cgpa < parseFloat(cgpaMin))) return false;\n            if (cgpaMax && (isNaN(cgpa) || cgpa > parseFloat(cgpaMax))) return false;\n            return true;\n        });\n        return filtered;\n    };\n    // Update filters and refetch when dependencies change (but not searchTerm)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            if (allStudents.length > 0) {\n                fetchStudents(1); // Reset to page 1 when filters change\n            }\n        }\n    }[\"StudentManagement.useEffect\"], [\n        selectedDepartment,\n        selectedYear,\n        debouncedSearchTerm,\n        selectedPassoutYear\n    ]);\n    // Filter students based on selected department, year, and search term\n    const filteredStudents = students; // Students are already filtered in fetchStudents\n    const handleStudentClick = (student)=>{\n        setSelectedStudent(student);\n        setEditedStudent({\n            ...student\n        });\n        setIsEditing(false);\n    };\n    const handleBackToList = ()=>{\n        setSelectedStudent(null);\n        setIsEditing(false);\n        setEditedStudent(null);\n    };\n    const handleBackToDepartments = ()=>{\n        setSelectedDepartment(null);\n        setSelectedYear('all');\n        setSearchTerm('');\n    };\n    const handleEdit = ()=>{\n        setIsEditing(true);\n    };\n    const handleSave = ()=>{\n        // Update the student in the list\n        setStudents((prev)=>prev.map((student)=>student.id === editedStudent.id ? editedStudent : student));\n        setSelectedStudent(editedStudent);\n        setIsEditing(false);\n    // Here you would typically make an API call to save the changes\n    };\n    const handleCancel = ()=>{\n        setEditedStudent({\n            ...selectedStudent\n        });\n        setIsEditing(false);\n    };\n    const handleInputChange = (field, value)=>{\n        setEditedStudent((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    // Handle retry button click\n    const handleRetry = ()=>{\n        setIsRetrying(true);\n        fetchStudents();\n    };\n    // Help developers find the correct API endpoint\n    const debugBackend = ()=>{\n        window.open('http://localhost:8000/admin/');\n    };\n    const handleSearch = ()=>{\n        // Force immediate search without waiting for debounce\n        setDebouncedSearchTerm(searchTerm);\n        setCurrentPage(1);\n    };\n    // Handle pagination\n    const handlePageChange = (newPage)=>{\n        if (newPage >= 1 && newPage <= totalPages) {\n            fetchStudents(newPage);\n        }\n    };\n    // Handle search input change\n    const handleSearchInputChange = (e)=>{\n        setSearchTerm(e.target.value);\n    // Don't trigger immediate search, let debounce handle it\n    };\n    // Handle search input key press\n    const handleSearchKeyDown = (e)=>{\n        if (e.key === 'Enter') {\n            e.preventDefault();\n            handleSearch();\n        }\n    };\n    if (loading) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 p-6 ml-20 overflow-y-auto h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center h-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mb-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                    lineNumber: 485,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-gray-600\",\n                    children: \"Loading students...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                    lineNumber: 486,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n            lineNumber: 484,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n        lineNumber: 483,\n        columnNumber: 5\n    }, this);\n    if (error && students.length === 0) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 p-6 ml-20 overflow-y-auto h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center h-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-red-500 mb-4 text-center max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-semibold text-lg mb-2\",\n                            children: \"Access Error\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 495,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 496,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 text-sm text-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Possible solutions:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                    lineNumber: 498,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"list-disc list-inside mt-2 text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Make sure you're logged in with admin credentials\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                            lineNumber: 500,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Check if your session has expired\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                            lineNumber: 501,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Verify Django server is running on port 8000\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                            lineNumber: 502,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Ensure proper permissions are set in Django\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                            lineNumber: 503,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                    lineNumber: 499,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 497,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                    lineNumber: 494,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-3 mt-4\",\n                    children: [\n                        !error.includes('login') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleRetry,\n                            className: \"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                            disabled: isRetrying,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_GraduationCap_RefreshCw_Save_Search_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-4 h-4 \".concat(isRetrying ? 'animate-spin' : '')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                    lineNumber: 514,\n                                    columnNumber: 15\n                                }, this),\n                                isRetrying ? 'Retrying...' : 'Retry'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 509,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>window.location.href = '/login',\n                            className: \"flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_GraduationCap_RefreshCw_Save_Search_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                    lineNumber: 522,\n                                    columnNumber: 13\n                                }, this),\n                                \"Go to Login\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 518,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                    lineNumber: 507,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n            lineNumber: 493,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n        lineNumber: 492,\n        columnNumber: 5\n    }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 p-6 ml-20 overflow-y-auto h-full\",\n        children: !selectedStudent ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: !selectedDepartment ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DepartmentCards__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                departmentOptions: departmentOptions,\n                departmentStats: departmentStats,\n                allStudents: allStudents,\n                onSelect: setSelectedDepartment\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                lineNumber: 535,\n                columnNumber: 13\n            }, this) : !selectedPassoutYear ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PassoutYearCards__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                departmentLabel: (_departmentOptions_find = departmentOptions.find((d)=>d.value === selectedDepartment)) === null || _departmentOptions_find === void 0 ? void 0 : _departmentOptions_find.label,\n                onBack: handleBackToDepartments,\n                getAvailablePassoutYears: getAvailablePassoutYears,\n                allStudents: allStudents,\n                selectedDepartment: selectedDepartment,\n                onSelectYear: setSelectedPassoutYear\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                lineNumber: 542,\n                columnNumber: 13\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StudentList__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                departmentLabel: (_departmentOptions_find1 = departmentOptions.find((d)=>d.value === selectedDepartment)) === null || _departmentOptions_find1 === void 0 ? void 0 : _departmentOptions_find1.label,\n                passoutYear: selectedPassoutYear,\n                onBack: ()=>setSelectedPassoutYear(null),\n                searchTerm: searchTerm,\n                handleSearchInputChange: handleSearchInputChange,\n                handleSearchKeyDown: handleSearchKeyDown,\n                cgpaMin: cgpaMin,\n                setCgpaMin: setCgpaMin,\n                cgpaMax: cgpaMax,\n                setCgpaMax: setCgpaMax,\n                handleSearch: handleSearch,\n                getFilteredStudents: getFilteredStudents,\n                currentPage: currentPage,\n                handlePageChange: handlePageChange,\n                handleStudentClick: handleStudentClick,\n                loading: loading\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                lineNumber: 551,\n                columnNumber: 13\n            }, this)\n        }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StudentProfile__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            selectedStudent: selectedStudent,\n            editedStudent: editedStudent,\n            isEditing: isEditing,\n            handleBackToList: handleBackToList,\n            handleEdit: handleEdit,\n            handleSave: handleSave,\n            handleCancel: handleCancel,\n            handleInputChange: handleInputChange,\n            departmentOptions: departmentOptions\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n            lineNumber: 572,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n        lineNumber: 531,\n        columnNumber: 5\n    }, this);\n}\n_s(StudentManagement, \"vV3ywjzqfJaaIG37j/kyv5UkL2w=\");\n_c = StudentManagement;\nvar _c;\n$RefreshReg$(_c, \"StudentManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/student-management/page.jsx\n"));

/***/ })

});