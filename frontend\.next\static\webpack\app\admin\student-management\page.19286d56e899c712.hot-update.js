"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/student-management/page",{

/***/ "(app-pages-browser)/./src/app/admin/student-management/StudentProfile.jsx":
/*!*************************************************************!*\
  !*** ./src/app/admin/student-management/StudentProfile.jsx ***!
  \*************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StudentProfile)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Edit_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Edit,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Edit_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Edit,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Edit_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Edit,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Edit_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Edit,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_FaBuilding_FaFileAlt_FaMapMarkerAlt_FaPhoneAlt_FaSpinner_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=FaBuilding,FaFileAlt,FaMapMarkerAlt,FaPhoneAlt,FaSpinner,FaUser!=!react-icons/fa */ \"(app-pages-browser)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var _api_students__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../api/students */ \"(app-pages-browser)/./src/api/students.js\");\n/* harmony import */ var _DocumentsModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./DocumentsModal */ \"(app-pages-browser)/./src/app/admin/student-management/DocumentsModal.jsx\");\n/* harmony import */ var _ResumeModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ResumeModal */ \"(app-pages-browser)/./src/app/admin/student-management/ResumeModal.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction StudentProfile(param) {\n    let { selectedStudent, editedStudent, isEditing, handleBackToList, handleEdit, handleSave, handleCancel, handleInputChange, departmentOptions } = param;\n    _s();\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [semesterMarksheets, setSemesterMarksheets] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isDocumentsModalOpen, setIsDocumentsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isResumeModalOpen, setIsResumeModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [companyStats, setCompanyStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        loading: false,\n        totalListings: 0,\n        eligibleJobs: 0\n    });\n    const [resumeCount, setResumeCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [lastResumeUpdate, setLastResumeUpdate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Use selectedStudent as the profile data, but allow fetching more details if needed\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentProfile.useEffect\": ()=>{\n            if (selectedStudent) {\n                setProfile(selectedStudent);\n                // Optionally fetch additional details if needed\n                fetchAdditionalDetails(selectedStudent.id);\n                // Fetch company statistics\n                fetchCompanyStats(selectedStudent.id);\n                // Fetch resume information\n                fetchResumeInfo(selectedStudent.id);\n            }\n        }\n    }[\"StudentProfile.useEffect\"], [\n        selectedStudent\n    ]);\n    // Fetch additional details if needed\n    const fetchAdditionalDetails = async (studentId)=>{\n        if (!studentId) return;\n        try {\n            setLoading(true);\n            // This could fetch semester marksheets or other detailed info not included in the list view\n            // const details = await studentsAPI.getStudentDetails(studentId);\n            // setSemesterMarksheets(details.semesterMarksheets || []);\n            setLoading(false);\n        } catch (err) {\n            console.error('Error fetching student details:', err);\n            setLoading(false);\n        }\n    };\n    // Fetch company statistics for the student\n    const fetchCompanyStats = async (studentId)=>{\n        if (!studentId) return;\n        try {\n            setCompanyStats((prev)=>({\n                    ...prev,\n                    loading: true\n                }));\n            // Mock implementation - replace with actual API call\n            // const stats = await studentsAPI.getStudentCompanyStats(studentId);\n            // For now, provide mock data\n            const mockStats = {\n                loading: false,\n                totalListings: 25,\n                eligibleJobs: 18\n            };\n            setCompanyStats(mockStats);\n        } catch (err) {\n            console.error('Error fetching company stats:', err);\n            setCompanyStats({\n                loading: false,\n                totalListings: 0,\n                eligibleJobs: 0\n            });\n        }\n    };\n    // Fetch resume information for the student\n    const fetchResumeInfo = async (studentId)=>{\n        if (!studentId) return;\n        try {\n            // Mock implementation - replace with actual API call\n            // const resumeInfo = await studentsAPI.getStudentResumes(studentId);\n            // For now, provide mock data\n            const mockResumeCount = 2;\n            const mockLastUpdate = new Date('2024-01-15').toISOString();\n            setResumeCount(mockResumeCount);\n            setLastResumeUpdate(mockLastUpdate);\n        } catch (err) {\n            console.error('Error fetching resume info:', err);\n            setResumeCount(0);\n            setLastResumeUpdate(null);\n        }\n    };\n    // Calculate overall CGPA\n    const calculateOverallCGPA = ()=>{\n        return (profile === null || profile === void 0 ? void 0 : profile.cgpa) || 'N/A';\n    };\n    // Calculate percentage from CGPA (approximation)\n    const calculatePercentage = (cgpa)=>{\n        if (!cgpa || cgpa === 'N/A') return '';\n        const numericCgpa = parseFloat(cgpa);\n        if (isNaN(numericCgpa)) return '';\n        return (numericCgpa * 9.5).toFixed(2) + '%';\n    };\n    // Format year range if available\n    const formatEducationPeriod = (joiningYear, passoutYear)=>{\n        if (joiningYear && passoutYear) {\n            return \"\".concat(joiningYear, \" - \").concat(passoutYear);\n        }\n        return 'N/A';\n    };\n    // Get year range for student\n    const getYearRange = ()=>{\n        if ((profile === null || profile === void 0 ? void 0 : profile.joining_year) && (profile === null || profile === void 0 ? void 0 : profile.passout_year)) {\n            return \"\".concat(profile.joining_year, \" - \").concat(profile.passout_year);\n        }\n        if ((editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.joining_year) && (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.passout_year)) {\n            return \"\".concat(editedStudent.joining_year, \" - \").concat(editedStudent.passout_year);\n        }\n        if ((profile === null || profile === void 0 ? void 0 : profile.year) || (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.year)) {\n            return (profile === null || profile === void 0 ? void 0 : profile.year) || (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.year);\n        }\n        return null;\n    };\n    // Get semester CGPA (mock implementation)\n    const getSemesterCGPA = (semester)=>{\n        var _semesterMarksheets_;\n        return ((_semesterMarksheets_ = semesterMarksheets[semester - 1]) === null || _semesterMarksheets_ === void 0 ? void 0 : _semesterMarksheets_.cgpa) || '-';\n    };\n    // Handle resume upload\n    const handleResumeUpload = async (file)=>{\n        try {\n            await _api_students__WEBPACK_IMPORTED_MODULE_3__.studentsAPI.uploadResume(file);\n            // Refresh resume info after upload\n            if (selectedStudent) {\n                fetchResumeInfo(selectedStudent.id);\n            }\n        } catch (err) {\n            console.error('Error uploading resume:', err);\n            alert('Failed to upload resume. Please try again.');\n        }\n    };\n    // Handle resume delete\n    const handleResumeDelete = async (resumeId)=>{\n        try {\n            await _api_students__WEBPACK_IMPORTED_MODULE_3__.studentsAPI.deleteResume(resumeId);\n            // Refresh resume info after delete\n            if (selectedStudent) {\n                fetchResumeInfo(selectedStudent.id);\n            }\n        } catch (err) {\n            console.error('Error deleting resume:', err);\n            alert('Failed to delete resume. Please try again.');\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBuilding_FaFileAlt_FaMapMarkerAlt_FaPhoneAlt_FaSpinner_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_6__.FaSpinner, {\n                    className: \"animate-spin text-blue-500 text-xl mr-2\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                    lineNumber: 187,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: \"Loading details...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                    lineNumber: 188,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n            lineNumber: 186,\n            columnNumber: 7\n        }, this);\n    }\n    if (!selectedStudent && !editedStudent) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center p-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-500\",\n                    children: \"No student selected\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                    lineNumber: 196,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleBackToList,\n                    className: \"mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                    children: \"Back to List\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                    lineNumber: 197,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n            lineNumber: 195,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleBackToList,\n                        className: \"flex items-center gap-2 text-blue-600 hover:text-blue-800 transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Edit_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                lineNumber: 215,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Back to List\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                lineNumber: 216,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleSave,\n                                    className: \"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors mr-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Edit_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Save\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleCancel,\n                                    className: \"px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Edit_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleEdit,\n                            className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Edit_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Edit\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                lineNumber: 246,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                            lineNumber: 242,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                        lineNumber: 219,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                lineNumber: 210,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-xl shadow-sm overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-blue-50 p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-16 h-16 bg-blue-500 text-white flex items-center justify-center rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.name) ? editedStudent.name[0].toUpperCase() : 'S'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-grow\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-xl font-semibold text-gray-800\",\n                                            children: isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.name) || '',\n                                                onChange: (e)=>handleInputChange('name', e.target.value),\n                                                className: \"w-full p-1 border rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 19\n                                            }, this) : (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.name) || 'Unknown Student'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1 text-gray-600\",\n                                            children: [\n                                                isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.rollNumber) || '',\n                                                    onChange: (e)=>handleInputChange('rollNumber', e.target.value),\n                                                    className: \"p-1 border rounded mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 19\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        \"ID: \",\n                                                        (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.rollNumber) || 'N/A'\n                                                    ]\n                                                }, void 0, true),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mx-2\",\n                                                    children: \"|\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 17\n                                                }, this),\n                                                isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.department) || '',\n                                                    onChange: (e)=>handleInputChange('department', e.target.value),\n                                                    className: \"p-1 border rounded mr-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"\",\n                                                            children: \"Select Department\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        departmentOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: option.value,\n                                                                children: option.label\n                                                            }, option.value, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                lineNumber: 303,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 19\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        \"Department: \",\n                                                        (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.department) || 'N/A'\n                                                    ]\n                                                }, void 0, true),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mx-2\",\n                                                    children: \"|\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 17\n                                                }, this),\n                                                isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.year) || '',\n                                                    onChange: (e)=>handleInputChange('year', e.target.value),\n                                                    className: \"p-1 border rounded\",\n                                                    placeholder: \"YYYY-YYYY\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 19\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        \"Year: \",\n                                                        getYearRange() || 'N/A'\n                                                    ]\n                                                }, void 0, true)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-100 px-3 py-1 rounded-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-blue-600 font-medium\",\n                                                children: [\n                                                    \"CGPA: \",\n                                                    calculateOverallCGPA()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 17\n                                            }, this),\n                                            calculateOverallCGPA() !== 'N/A' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500 ml-1\",\n                                                children: [\n                                                    \"(\",\n                                                    calculatePercentage(calculateOverallCGPA()),\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                            lineNumber: 259,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                        lineNumber: 258,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-medium mb-4 text-gray-800 border-b pb-2\",\n                                            children: \"Personal Information\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 344,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-gray-600 text-sm mb-1\",\n                                                            children: \"Email\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 348,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"email\",\n                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.email) || '',\n                                                            onChange: (e)=>handleInputChange('email', e.target.value),\n                                                            className: \"w-full p-2 border rounded-lg\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-800\",\n                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.email) || 'N/A'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 357,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-gray-600 text-sm mb-1\",\n                                                            children: \"Phone\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 362,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"tel\",\n                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.phone) || '',\n                                                            onChange: (e)=>handleInputChange('phone', e.target.value),\n                                                            className: \"w-full p-2 border rounded-lg\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 364,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-800\",\n                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.phone) || 'N/A'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 371,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-gray-600 text-sm mb-1\",\n                                                            children: \"Date of Birth\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"date\",\n                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.dateOfBirth) || '',\n                                                            onChange: (e)=>handleInputChange('dateOfBirth', e.target.value),\n                                                            className: \"w-full p-2 border rounded-lg\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 378,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-800\",\n                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.dateOfBirth) || 'N/A'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 385,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-gray-600 text-sm mb-1\",\n                                                            children: \"Parent Contact\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 390,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"tel\",\n                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.parentContact) || '',\n                                                            onChange: (e)=>handleInputChange('parentContact', e.target.value),\n                                                            className: \"w-full p-2 border rounded-lg\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 392,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-800\",\n                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.parentContact) || 'N/A'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 399,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                    lineNumber: 343,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-medium mb-4 text-gray-800 border-b pb-2\",\n                                            children: \"Address & Education\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-gray-600 text-sm mb-1\",\n                                                            children: \"Address\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 411,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.address) || '',\n                                                            onChange: (e)=>handleInputChange('address', e.target.value),\n                                                            className: \"w-full p-2 border rounded-lg\",\n                                                            rows: 2\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 413,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-800\",\n                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.address) || 'N/A'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 420,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 410,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-gray-600 text-sm mb-1\",\n                                                            children: \"Education\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 425,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.education) || '',\n                                                            onChange: (e)=>handleInputChange('education', e.target.value),\n                                                            className: \"w-full p-2 border rounded-lg\",\n                                                            rows: 2\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 427,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-800\",\n                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.education) || 'N/A'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 434,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                    lineNumber: 406,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"md:col-span-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-medium mb-4 text-gray-800 border-b pb-2\",\n                                            children: \"Skills\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 442,\n                                            columnNumber: 15\n                                        }, this),\n                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: Array.isArray(editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.skills) ? editedStudent.skills.join(', ') : (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.skills) || '',\n                                            onChange: (e)=>handleInputChange('skills', e.target.value.split(',').map((s)=>s.trim())),\n                                            className: \"w-full p-2 border rounded-lg\",\n                                            rows: 2,\n                                            placeholder: \"Enter skills separated by commas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 445,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: Array.isArray(editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.skills) && editedStudent.skills.length > 0 ? editedStudent.skills.map((skill, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm\",\n                                                    children: skill\n                                                }, index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 456,\n                                                    columnNumber: 23\n                                                }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"No skills listed\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 464,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 453,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                    lineNumber: 441,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                            lineNumber: 341,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                        lineNumber: 340,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-6 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg p-5 shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-6 text-gray-800\",\n                                        children: \"Academic\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-800\",\n                                                        children: \"Semester Wise score\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-blue-50 inline-flex items-center px-3 py-1 rounded-full ml-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-blue-600 font-medium\",\n                                                                children: calculateOverallCGPA()\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                lineNumber: 481,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-500 ml-1\",\n                                                                children: \"CGPA\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                lineNumber: 482,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-blue-600 ml-2\",\n                                                                children: calculatePercentage(calculateOverallCGPA())\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                lineNumber: 483,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 480,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 478,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-lg font-semibold text-gray-800\",\n                                                children: isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.joining_year) || '',\n                                                            onChange: (e)=>handleInputChange('joining_year', e.target.value),\n                                                            className: \"w-20 p-1 border rounded text-sm\",\n                                                            placeholder: \"2020\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 489,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"-\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 496,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.passout_year) || '',\n                                                            onChange: (e)=>handleInputChange('passout_year', e.target.value),\n                                                            className: \"w-20 p-1 border rounded text-sm\",\n                                                            placeholder: \"2024\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 497,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 488,\n                                                    columnNumber: 21\n                                                }, this) : formatEducationPeriod(editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.joining_year, editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.passout_year)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 486,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 477,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"overflow-x-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                className: \"w-full border-collapse border border-gray-300\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            className: \"bg-gray-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"border border-gray-300 px-4 py-3 text-sm font-semibold text-gray-700\",\n                                                                    children: \"Sem\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                    lineNumber: 516,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                [\n                                                                    1,\n                                                                    2,\n                                                                    3,\n                                                                    4,\n                                                                    5,\n                                                                    6,\n                                                                    7,\n                                                                    8\n                                                                ].map((sem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"border border-gray-300 px-4 py-3 text-sm font-semibold text-gray-700\",\n                                                                        children: sem\n                                                                    }, sem, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                        lineNumber: 518,\n                                                                        columnNumber: 25\n                                                                    }, this))\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 515,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 514,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"border border-gray-300 px-4 py-3 text-sm font-medium text-gray-700\",\n                                                                    children: \"Cgpa\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                    lineNumber: 524,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                [\n                                                                    1,\n                                                                    2,\n                                                                    3,\n                                                                    4,\n                                                                    5,\n                                                                    6,\n                                                                    7,\n                                                                    8\n                                                                ].map((sem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"border border-gray-300 px-4 py-3 text-sm text-gray-700\",\n                                                                        children: getSemesterCGPA(sem)\n                                                                    }, sem, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                        lineNumber: 526,\n                                                                        columnNumber: 25\n                                                                    }, this))\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 523,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 522,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 513,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 512,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 511,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                        className: \"my-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 535,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-gray-800\",\n                                                                children: \"Class XII\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                lineNumber: 541,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-blue-50 inline-flex items-center px-3 py-1 rounded-full ml-3\",\n                                                                children: isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.twelfth_cgpa) || '',\n                                                                            onChange: (e)=>handleInputChange('twelfth_cgpa', e.target.value),\n                                                                            className: \"w-16 p-1 border rounded text-sm text-blue-600 bg-transparent\",\n                                                                            placeholder: \"9.5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 545,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-500 ml-1\",\n                                                                            children: \"CGPA\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 552,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.twelfth_percentage) || '',\n                                                                            onChange: (e)=>handleInputChange('twelfth_percentage', e.target.value),\n                                                                            className: \"w-16 p-1 border rounded text-sm text-blue-600 bg-transparent ml-2\",\n                                                                            placeholder: \"95%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 553,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-blue-600 font-medium\",\n                                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.twelfth_cgpa) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 563,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-500 ml-1\",\n                                                                            children: \"CGPA\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 564,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-blue-600 ml-2\",\n                                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.twelfth_percentage) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 565,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                lineNumber: 542,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 540,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-lg font-semibold text-gray-800\",\n                                                        children: isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.twelfth_year_of_passing) || '',\n                                                            onChange: (e)=>handleInputChange('twelfth_year_of_passing', e.target.value),\n                                                            className: \"w-20 p-1 border rounded text-sm\",\n                                                            placeholder: \"2020\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 572,\n                                                            columnNumber: 21\n                                                        }, this) : (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.twelfth_year_of_passing) ? \"\".concat(parseInt(editedStudent.twelfth_year_of_passing) - 2, \" - \").concat(editedStudent.twelfth_year_of_passing) : '-'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 570,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 539,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start mb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-6 w-full\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-500 text-sm w-[120px]\",\n                                                                            children: \"College :\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 590,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.twelfth_school) || '',\n                                                                            onChange: (e)=>handleInputChange('twelfth_school', e.target.value),\n                                                                            className: \"flex-1 p-1 border rounded text-sm\",\n                                                                            placeholder: \"School/College name\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 592,\n                                                                            columnNumber: 25\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-700 font-medium\",\n                                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.twelfth_school) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 600,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                    lineNumber: 589,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-500 text-sm w-[120px]\",\n                                                                            children: \"Board :\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 604,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.twelfth_board) || '',\n                                                                            onChange: (e)=>handleInputChange('twelfth_board', e.target.value),\n                                                                            className: \"flex-1 p-1 border rounded text-sm\",\n                                                                            placeholder: \"Board name\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 606,\n                                                                            columnNumber: 25\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-700 font-medium\",\n                                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.twelfth_board) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 614,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                    lineNumber: 603,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 588,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-500 text-sm w-[120px]\",\n                                                                            children: \"Location :\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 620,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.twelfth_location) || '',\n                                                                            onChange: (e)=>handleInputChange('twelfth_location', e.target.value),\n                                                                            className: \"flex-1 p-1 border rounded text-sm\",\n                                                                            placeholder: \"City, State\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 622,\n                                                                            columnNumber: 25\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-700 font-medium\",\n                                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.twelfth_location) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 630,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                    lineNumber: 619,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-500 text-sm w-[120px]\",\n                                                                            children: \"Specialization :\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 634,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.twelfth_specialization) || '',\n                                                                            onChange: (e)=>handleInputChange('twelfth_specialization', e.target.value),\n                                                                            className: \"flex-1 p-1 border rounded text-sm\",\n                                                                            placeholder: \"Science/Commerce/Arts\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 636,\n                                                                            columnNumber: 25\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-700 font-medium\",\n                                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.twelfth_specialization) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 644,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                    lineNumber: 633,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 618,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 587,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 586,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 538,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                        className: \"my-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 656,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-gray-800\",\n                                                                children: \"Class X\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                lineNumber: 662,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-blue-50 inline-flex items-center px-3 py-1 rounded-full ml-3\",\n                                                                children: isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.tenth_cgpa) || '',\n                                                                            onChange: (e)=>handleInputChange('tenth_cgpa', e.target.value),\n                                                                            className: \"w-16 p-1 border rounded text-sm text-blue-600 bg-transparent\",\n                                                                            placeholder: \"9.5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 666,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-500 ml-1\",\n                                                                            children: \"CGPA\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 673,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.tenth_percentage) || '',\n                                                                            onChange: (e)=>handleInputChange('tenth_percentage', e.target.value),\n                                                                            className: \"w-16 p-1 border rounded text-sm text-blue-600 bg-transparent ml-2\",\n                                                                            placeholder: \"95%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 674,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-blue-600 font-medium\",\n                                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.tenth_cgpa) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 684,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-500 ml-1\",\n                                                                            children: \"CGPA\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 685,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-blue-600 ml-2\",\n                                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.tenth_percentage) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 686,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                lineNumber: 663,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 661,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-lg font-semibold text-gray-800\",\n                                                        children: isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.tenth_year_of_passing) || '',\n                                                            onChange: (e)=>handleInputChange('tenth_year_of_passing', e.target.value),\n                                                            className: \"w-20 p-1 border rounded text-sm\",\n                                                            placeholder: \"2018\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 693,\n                                                            columnNumber: 21\n                                                        }, this) : (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.tenth_year_of_passing) ? \"\".concat(parseInt(editedStudent.tenth_year_of_passing) - 1, \" - \").concat(editedStudent.tenth_year_of_passing) : '-'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 691,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 660,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start mb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-6 w-full\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-500 text-sm w-[120px]\",\n                                                                            children: \"School :\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 712,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.tenth_school) || '',\n                                                                            onChange: (e)=>handleInputChange('tenth_school', e.target.value),\n                                                                            className: \"flex-1 p-1 border rounded text-sm\",\n                                                                            placeholder: \"School name\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 714,\n                                                                            columnNumber: 25\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-700 font-medium\",\n                                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.tenth_school) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 722,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                    lineNumber: 711,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-500 text-sm w-[120px]\",\n                                                                            children: \"Board :\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 726,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.tenth_board) || '',\n                                                                            onChange: (e)=>handleInputChange('tenth_board', e.target.value),\n                                                                            className: \"flex-1 p-1 border rounded text-sm\",\n                                                                            placeholder: \"Board name\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 728,\n                                                                            columnNumber: 25\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-700 font-medium\",\n                                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.tenth_board) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 736,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                    lineNumber: 725,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 710,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-500 text-sm w-[120px]\",\n                                                                            children: \"Location :\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 742,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.tenth_location) || '',\n                                                                            onChange: (e)=>handleInputChange('tenth_location', e.target.value),\n                                                                            className: \"flex-1 p-1 border rounded text-sm\",\n                                                                            placeholder: \"City, State\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 744,\n                                                                            columnNumber: 25\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-700 font-medium\",\n                                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.tenth_location) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 752,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                    lineNumber: 741,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-500 text-sm w-[120px]\",\n                                                                            children: \"Specialization :\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 756,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.tenth_specialization) || '',\n                                                                            onChange: (e)=>handleInputChange('tenth_specialization', e.target.value),\n                                                                            className: \"flex-1 p-1 border rounded text-sm\",\n                                                                            placeholder: \"General/Other\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 758,\n                                                                            columnNumber: 25\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-700 font-medium\",\n                                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.tenth_specialization) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 766,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                    lineNumber: 755,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 740,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 709,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 708,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 659,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                lineNumber: 475,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg p-5 shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-4 text-gray-800\",\n                                        children: \"Companies\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 777,\n                                        columnNumber: 13\n                                    }, this),\n                                    companyStats.loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center py-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBuilding_FaFileAlt_FaMapMarkerAlt_FaPhoneAlt_FaSpinner_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_6__.FaSpinner, {\n                                                className: \"animate-spin text-blue-500 text-xl mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 781,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Loading company data...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 782,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 780,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-500 text-sm\",\n                                                            children: \"Total Listings\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 789,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-semibold text-gray-700\",\n                                                            children: companyStats.totalListings\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 790,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 788,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 787,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-500 text-sm\",\n                                                            children: \"Eligible Jobs\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 797,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-semibold text-gray-700\",\n                                                            children: companyStats.eligibleJobs\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 798,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 796,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 795,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                lineNumber: 776,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                        lineNumber: 473,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-3 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg p-5 shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-4 text-gray-800\",\n                                        children: \"My Files\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 810,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center p-2 mb-3 cursor-pointer hover:bg-gray-50 rounded-lg transition-colors\",\n                                        onClick: ()=>setIsResumeModalOpen(true),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-blue-600\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"h-6 w-6\",\n                                                        fill: \"none\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        stroke: \"currentColor\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 819,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 818,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 817,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 816,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-grow\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-medium text-gray-700\",\n                                                        children: \"Resumes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 824,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: resumeCount > 0 ? \"\".concat(resumeCount, \" resume\").concat(resumeCount > 1 ? 's' : '', \" uploaded\") + (lastResumeUpdate ? \" • Last updated \".concat(new Date(lastResumeUpdate).toLocaleDateString()) : '') : 'No resumes uploaded'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 825,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 823,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-50 px-3 py-1 rounded-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-600 font-medium\",\n                                                    children: resumeCount\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 834,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 833,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 812,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center p-2 cursor-pointer hover:bg-gray-50 rounded-lg transition-colors\",\n                                        onClick: ()=>setIsDocumentsModalOpen(true),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-blue-600\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"h-6 w-6\",\n                                                        fill: \"none\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        stroke: \"currentColor\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 845,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 844,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 843,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 842,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-grow\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-medium text-gray-700\",\n                                                        children: \"Documents\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 850,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Academic certificates and marksheets\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 851,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 849,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-50 px-3 py-1 rounded-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-600 font-medium\",\n                                                    children: ((profile === null || profile === void 0 ? void 0 : profile.tenth_certificate) ? 1 : 0) + ((profile === null || profile === void 0 ? void 0 : profile.twelfth_certificate) ? 1 : 0) + semesterMarksheets.length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 854,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 853,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 838,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                lineNumber: 809,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg p-5 shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold text-gray-800\",\n                                            children: \"CURRENT ADDRESS\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 866,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 865,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500 w-20\",\n                                                        children: \"City\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 871,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-700\",\n                                                        children: [\n                                                            \": \",\n                                                            (profile === null || profile === void 0 ? void 0 : profile.city) || '-'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 872,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 870,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500 w-20\",\n                                                        children: \"District\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 875,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-700\",\n                                                        children: [\n                                                            \": \",\n                                                            (profile === null || profile === void 0 ? void 0 : profile.district) || '-'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 876,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 874,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500 w-20\",\n                                                        children: \"State\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 879,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-700\",\n                                                        children: [\n                                                            \": \",\n                                                            (profile === null || profile === void 0 ? void 0 : profile.state) || '-'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 880,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 878,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500 w-20\",\n                                                        children: \"Pin Code\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 883,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-700\",\n                                                        children: [\n                                                            \": \",\n                                                            (profile === null || profile === void 0 ? void 0 : profile.pincode) || '-'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 884,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 882,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500 w-20\",\n                                                        children: \"Country\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 887,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-700\",\n                                                        children: [\n                                                            \": \",\n                                                            (profile === null || profile === void 0 ? void 0 : profile.country) || '-'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 888,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 886,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500 w-20\",\n                                                        children: \"Address\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 891,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-700\",\n                                                        children: [\n                                                            \": \",\n                                                            (profile === null || profile === void 0 ? void 0 : profile.address) || '-'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 892,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 890,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 869,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                lineNumber: 864,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                        lineNumber: 807,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ResumeModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        isOpen: isResumeModalOpen,\n                        onClose: ()=>setIsResumeModalOpen(false),\n                        resume: (profile === null || profile === void 0 ? void 0 : profile.resume_url) || (profile === null || profile === void 0 ? void 0 : profile.resume),\n                        onUpload: handleResumeUpload,\n                        onDelete: handleResumeDelete\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                        lineNumber: 900,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DocumentsModal__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        isOpen: isDocumentsModalOpen,\n                        onClose: ()=>setIsDocumentsModalOpen(false),\n                        documents: {\n                            tenth: (profile === null || profile === void 0 ? void 0 : profile.tenth_certificate_url) || (profile === null || profile === void 0 ? void 0 : profile.tenth_certificate),\n                            twelfth: (profile === null || profile === void 0 ? void 0 : profile.twelfth_certificate_url) || (profile === null || profile === void 0 ? void 0 : profile.twelfth_certificate),\n                            semesterMarksheets: semesterMarksheets\n                        },\n                        onUploadCertificate: _api_students__WEBPACK_IMPORTED_MODULE_3__.studentsAPI.uploadCertificate,\n                        onUploadMarksheet: _api_students__WEBPACK_IMPORTED_MODULE_3__.studentsAPI.uploadSemesterMarksheet\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                        lineNumber: 909,\n                        columnNumber: 7\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                lineNumber: 256,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n        lineNumber: 208,\n        columnNumber: 5\n    }, this);\n}\n_s(StudentProfile, \"ErkQrXcp6uhP342rBjEs2qSg9w8=\");\n_c = StudentProfile;\nvar _c;\n$RefreshReg$(_c, \"StudentProfile\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/student-management/StudentProfile.jsx\n"));

/***/ })

});