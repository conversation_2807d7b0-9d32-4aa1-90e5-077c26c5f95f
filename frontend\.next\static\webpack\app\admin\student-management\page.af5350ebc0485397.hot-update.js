"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/student-management/page",{

/***/ "(app-pages-browser)/./src/app/admin/student-management/StudentProfile.jsx":
/*!*************************************************************!*\
  !*** ./src/app/admin/student-management/StudentProfile.jsx ***!
  \*************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StudentProfile)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Edit_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Edit,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Edit_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Edit,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Edit_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Edit,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Edit_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Edit,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_FaBuilding_FaFileAlt_FaMapMarkerAlt_FaPhoneAlt_FaSpinner_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=FaBuilding,FaFileAlt,FaMapMarkerAlt,FaPhoneAlt,FaSpinner,FaUser!=!react-icons/fa */ \"(app-pages-browser)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var _api_students__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../api/students */ \"(app-pages-browser)/./src/api/students.js\");\n/* harmony import */ var _DocumentsModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./DocumentsModal */ \"(app-pages-browser)/./src/app/admin/student-management/DocumentsModal.jsx\");\n/* harmony import */ var _ResumeModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ResumeModal */ \"(app-pages-browser)/./src/app/admin/student-management/ResumeModal.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction StudentProfile(param) {\n    let { selectedStudent, editedStudent, isEditing, handleBackToList, handleEdit, handleSave, handleCancel, handleInputChange, departmentOptions } = param;\n    _s();\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [semesterMarksheets, setSemesterMarksheets] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isDocumentsModalOpen, setIsDocumentsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isResumeModalOpen, setIsResumeModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Use selectedStudent as the profile data, but allow fetching more details if needed\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentProfile.useEffect\": ()=>{\n            if (selectedStudent) {\n                setProfile(selectedStudent);\n                // Optionally fetch additional details if needed\n                fetchAdditionalDetails(selectedStudent.id);\n            }\n        }\n    }[\"StudentProfile.useEffect\"], [\n        selectedStudent\n    ]);\n    // Fetch additional details if needed\n    const fetchAdditionalDetails = async (studentId)=>{\n        if (!studentId) return;\n        try {\n            setLoading(true);\n            // This could fetch semester marksheets or other detailed info not included in the list view\n            // const details = await studentsAPI.getStudentDetails(studentId);\n            // setSemesterMarksheets(details.semesterMarksheets || []);\n            setLoading(false);\n        } catch (err) {\n            console.error('Error fetching student details:', err);\n            setLoading(false);\n        }\n    };\n    // Calculate overall CGPA\n    const calculateOverallCGPA = ()=>{\n        return (profile === null || profile === void 0 ? void 0 : profile.cgpa) || 'N/A';\n    };\n    // Calculate percentage from CGPA (approximation)\n    const calculatePercentage = (cgpa)=>{\n        if (!cgpa || cgpa === 'N/A') return '';\n        const numericCgpa = parseFloat(cgpa);\n        if (isNaN(numericCgpa)) return '';\n        return (numericCgpa * 9.5).toFixed(2) + '%';\n    };\n    // Format year range if available\n    const formatEducationPeriod = (joiningYear, passoutYear)=>{\n        if (joiningYear && passoutYear) {\n            return \"\".concat(joiningYear, \" - \").concat(passoutYear);\n        }\n        return 'N/A';\n    };\n    // Get year range for student\n    const getYearRange = ()=>{\n        if ((profile === null || profile === void 0 ? void 0 : profile.joining_year) && (profile === null || profile === void 0 ? void 0 : profile.passout_year)) {\n            return \"\".concat(profile.joining_year, \" - \").concat(profile.passout_year);\n        }\n        if ((editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.joining_year) && (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.passout_year)) {\n            return \"\".concat(editedStudent.joining_year, \" - \").concat(editedStudent.passout_year);\n        }\n        if ((profile === null || profile === void 0 ? void 0 : profile.year) || (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.year)) {\n            return (profile === null || profile === void 0 ? void 0 : profile.year) || (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.year);\n        }\n        return null;\n    };\n    // Get semester CGPA (mock implementation)\n    const getSemesterCGPA = (semester)=>{\n        var _semesterMarksheets_;\n        return ((_semesterMarksheets_ = semesterMarksheets[semester - 1]) === null || _semesterMarksheets_ === void 0 ? void 0 : _semesterMarksheets_.cgpa) || '-';\n    };\n    // Handle resume upload\n    const handleResumeUpload = async (file)=>{\n        try {\n            await _api_students__WEBPACK_IMPORTED_MODULE_3__.studentsAPI.uploadResume(file);\n            // Refresh profile data if needed\n            if (selectedStudent) {\n                fetchAdditionalDetails(selectedStudent.id);\n            }\n        } catch (err) {\n            console.error('Error uploading resume:', err);\n            alert('Failed to upload resume. Please try again.');\n        }\n    };\n    // Handle resume delete\n    const handleResumeDelete = async (resumeId)=>{\n        try {\n            await _api_students__WEBPACK_IMPORTED_MODULE_3__.studentsAPI.deleteResume(resumeId);\n            // Refresh profile data if needed\n            if (selectedStudent) {\n                fetchAdditionalDetails(selectedStudent.id);\n            }\n        } catch (err) {\n            console.error('Error deleting resume:', err);\n            alert('Failed to delete resume. Please try again.');\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBuilding_FaFileAlt_FaMapMarkerAlt_FaPhoneAlt_FaSpinner_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_6__.FaSpinner, {\n                    className: \"animate-spin text-blue-500 text-xl mr-2\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                    lineNumber: 125,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: \"Loading details...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                    lineNumber: 126,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n            lineNumber: 124,\n            columnNumber: 7\n        }, this);\n    }\n    if (!selectedStudent && !editedStudent) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center p-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-500\",\n                    children: \"No student selected\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                    lineNumber: 134,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleBackToList,\n                    className: \"mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                    children: \"Back to List\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                    lineNumber: 135,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n            lineNumber: 133,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleBackToList,\n                        className: \"flex items-center gap-2 text-blue-600 hover:text-blue-800 transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Edit_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                lineNumber: 153,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Back to List\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                lineNumber: 154,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleSave,\n                                    className: \"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors mr-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Edit_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Save\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleCancel,\n                                    className: \"px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Edit_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleEdit,\n                            className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Edit_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Edit\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                lineNumber: 184,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                            lineNumber: 180,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                lineNumber: 148,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-xl shadow-sm overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-blue-50 p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-16 h-16 bg-blue-500 text-white flex items-center justify-center rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.name) ? editedStudent.name[0].toUpperCase() : 'S'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-grow\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-xl font-semibold text-gray-800\",\n                                            children: isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.name) || '',\n                                                onChange: (e)=>handleInputChange('name', e.target.value),\n                                                className: \"w-full p-1 border rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 19\n                                            }, this) : (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.name) || 'Unknown Student'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1 text-gray-600\",\n                                            children: [\n                                                isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.rollNumber) || '',\n                                                    onChange: (e)=>handleInputChange('rollNumber', e.target.value),\n                                                    className: \"p-1 border rounded mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 19\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        \"ID: \",\n                                                        (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.rollNumber) || 'N/A'\n                                                    ]\n                                                }, void 0, true),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mx-2\",\n                                                    children: \"|\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 17\n                                                }, this),\n                                                isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.department) || '',\n                                                    onChange: (e)=>handleInputChange('department', e.target.value),\n                                                    className: \"p-1 border rounded mr-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"\",\n                                                            children: \"Select Department\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 239,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        departmentOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: option.value,\n                                                                children: option.label\n                                                            }, option.value, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                lineNumber: 241,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 19\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        \"Department: \",\n                                                        (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.department) || 'N/A'\n                                                    ]\n                                                }, void 0, true),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mx-2\",\n                                                    children: \"|\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 17\n                                                }, this),\n                                                isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.year) || '',\n                                                    onChange: (e)=>handleInputChange('year', e.target.value),\n                                                    className: \"p-1 border rounded\",\n                                                    placeholder: \"YYYY-YYYY\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 19\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        \"Year: \",\n                                                        getYearRange() || 'N/A'\n                                                    ]\n                                                }, void 0, true)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-100 px-3 py-1 rounded-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-blue-600 font-medium\",\n                                                children: [\n                                                    \"CGPA: \",\n                                                    calculateOverallCGPA()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 17\n                                            }, this),\n                                            calculateOverallCGPA() !== 'N/A' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500 ml-1\",\n                                                children: [\n                                                    \"(\",\n                                                    calculatePercentage(calculateOverallCGPA()),\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                            lineNumber: 197,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                        lineNumber: 196,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-medium mb-4 text-gray-800 border-b pb-2\",\n                                            children: \"Personal Information\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-gray-600 text-sm mb-1\",\n                                                            children: \"Email\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 286,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"email\",\n                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.email) || '',\n                                                            onChange: (e)=>handleInputChange('email', e.target.value),\n                                                            className: \"w-full p-2 border rounded-lg\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 288,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-800\",\n                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.email) || 'N/A'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-gray-600 text-sm mb-1\",\n                                                            children: \"Phone\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"tel\",\n                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.phone) || '',\n                                                            onChange: (e)=>handleInputChange('phone', e.target.value),\n                                                            className: \"w-full p-2 border rounded-lg\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 302,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-800\",\n                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.phone) || 'N/A'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 309,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-gray-600 text-sm mb-1\",\n                                                            children: \"Date of Birth\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 314,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"date\",\n                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.dateOfBirth) || '',\n                                                            onChange: (e)=>handleInputChange('dateOfBirth', e.target.value),\n                                                            className: \"w-full p-2 border rounded-lg\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 316,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-800\",\n                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.dateOfBirth) || 'N/A'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 323,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-gray-600 text-sm mb-1\",\n                                                            children: \"Parent Contact\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 328,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"tel\",\n                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.parentContact) || '',\n                                                            onChange: (e)=>handleInputChange('parentContact', e.target.value),\n                                                            className: \"w-full p-2 border rounded-lg\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 330,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-800\",\n                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.parentContact) || 'N/A'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 337,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-medium mb-4 text-gray-800 border-b pb-2\",\n                                            children: \"Address & Education\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-gray-600 text-sm mb-1\",\n                                                            children: \"Address\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 349,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.address) || '',\n                                                            onChange: (e)=>handleInputChange('address', e.target.value),\n                                                            className: \"w-full p-2 border rounded-lg\",\n                                                            rows: 2\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 351,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-800\",\n                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.address) || 'N/A'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 358,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-gray-600 text-sm mb-1\",\n                                                            children: \"Education\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 363,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            value: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.education) || '',\n                                                            onChange: (e)=>handleInputChange('education', e.target.value),\n                                                            className: \"w-full p-2 border rounded-lg\",\n                                                            rows: 2\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 365,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-800\",\n                                                            children: (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.education) || 'N/A'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 372,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"md:col-span-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-medium mb-4 text-gray-800 border-b pb-2\",\n                                            children: \"Skills\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 15\n                                        }, this),\n                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: Array.isArray(editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.skills) ? editedStudent.skills.join(', ') : (editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.skills) || '',\n                                            onChange: (e)=>handleInputChange('skills', e.target.value.split(',').map((s)=>s.trim())),\n                                            className: \"w-full p-2 border rounded-lg\",\n                                            rows: 2,\n                                            placeholder: \"Enter skills separated by commas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 383,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: Array.isArray(editedStudent === null || editedStudent === void 0 ? void 0 : editedStudent.skills) && editedStudent.skills.length > 0 ? editedStudent.skills.map((skill, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm\",\n                                                    children: skill\n                                                }, index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 394,\n                                                    columnNumber: 23\n                                                }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"No skills listed\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                    lineNumber: 379,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                            lineNumber: 279,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                        lineNumber: 278,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-6 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg p-5 shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-6 text-gray-800\",\n                                        children: \"Academic\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-800\",\n                                                        children: \"Semester Wise score\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-blue-50 inline-flex items-center px-3 py-1 rounded-full ml-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-blue-600 font-medium\",\n                                                                children: calculateOverallCGPA()\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                lineNumber: 419,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-500 ml-1\",\n                                                                children: \"CGPA\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                lineNumber: 420,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-blue-600 ml-2\",\n                                                                children: calculatePercentage(calculateOverallCGPA())\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                lineNumber: 421,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 418,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 416,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-lg font-semibold text-gray-800\",\n                                                children: formatEducationPeriod(profile === null || profile === void 0 ? void 0 : profile.joining_year, profile === null || profile === void 0 ? void 0 : profile.passout_year)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"overflow-x-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                className: \"w-full border-collapse border border-gray-300\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            className: \"bg-gray-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"border border-gray-300 px-4 py-3 text-sm font-semibold text-gray-700\",\n                                                                    children: \"Sem\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                    lineNumber: 434,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                [\n                                                                    1,\n                                                                    2,\n                                                                    3,\n                                                                    4,\n                                                                    5,\n                                                                    6,\n                                                                    7,\n                                                                    8\n                                                                ].map((sem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"border border-gray-300 px-4 py-3 text-sm font-semibold text-gray-700\",\n                                                                        children: sem\n                                                                    }, sem, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                        lineNumber: 436,\n                                                                        columnNumber: 25\n                                                                    }, this))\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 433,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 432,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"border border-gray-300 px-4 py-3 text-sm font-medium text-gray-700\",\n                                                                    children: \"Cgpa\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                    lineNumber: 442,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                [\n                                                                    1,\n                                                                    2,\n                                                                    3,\n                                                                    4,\n                                                                    5,\n                                                                    6,\n                                                                    7,\n                                                                    8\n                                                                ].map((sem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"border border-gray-300 px-4 py-3 text-sm text-gray-700\",\n                                                                        children: getSemesterCGPA(sem)\n                                                                    }, sem, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                        lineNumber: 444,\n                                                                        columnNumber: 25\n                                                                    }, this))\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 441,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 440,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 431,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 430,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 429,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                        className: \"my-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-gray-800\",\n                                                                children: \"Class XII\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                lineNumber: 459,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-blue-50 inline-flex items-center px-3 py-1 rounded-full ml-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-blue-600 font-medium\",\n                                                                        children: (profile === null || profile === void 0 ? void 0 : profile.twelfth_cgpa) || '-'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                        lineNumber: 461,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-gray-500 ml-1\",\n                                                                        children: \"CGPA\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                        lineNumber: 462,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-blue-600 ml-2\",\n                                                                        children: (profile === null || profile === void 0 ? void 0 : profile.twelfth_percentage) || '-'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                        lineNumber: 463,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                lineNumber: 460,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 458,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-lg font-semibold text-gray-800\",\n                                                        children: (profile === null || profile === void 0 ? void 0 : profile.twelfth_year_of_passing) ? \"\".concat(parseInt(profile.twelfth_year_of_passing) - 2, \" - \").concat(profile.twelfth_year_of_passing) : '-'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 466,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start mb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-6 w-full\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-500 text-sm w-[120px]\",\n                                                                            children: \"College :\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 476,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-700 font-medium\",\n                                                                            children: (profile === null || profile === void 0 ? void 0 : profile.twelfth_school) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 477,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                    lineNumber: 475,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-500 text-sm w-[120px]\",\n                                                                            children: \"Board :\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 480,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-700 font-medium\",\n                                                                            children: (profile === null || profile === void 0 ? void 0 : profile.twelfth_board) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 481,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                    lineNumber: 479,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 474,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-500 text-sm w-[120px]\",\n                                                                            children: \"Location :\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 486,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-700 font-medium\",\n                                                                            children: (profile === null || profile === void 0 ? void 0 : profile.city) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 487,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                    lineNumber: 485,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-500 text-sm w-[120px]\",\n                                                                            children: \"Specialization :\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 490,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-700 font-medium\",\n                                                                            children: (profile === null || profile === void 0 ? void 0 : profile.twelfth_specialization) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 491,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                    lineNumber: 489,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 484,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 473,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 456,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                        className: \"my-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 502,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-gray-800\",\n                                                                children: \"Class X\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                lineNumber: 508,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-blue-50 inline-flex items-center px-3 py-1 rounded-full ml-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-blue-600 font-medium\",\n                                                                        children: (profile === null || profile === void 0 ? void 0 : profile.tenth_cgpa) || '-'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                        lineNumber: 510,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-gray-500 ml-1\",\n                                                                        children: \"CGPA\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                        lineNumber: 511,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-blue-600 ml-2\",\n                                                                        children: (profile === null || profile === void 0 ? void 0 : profile.tenth_percentage) || '-'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                        lineNumber: 512,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                lineNumber: 509,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 507,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-lg font-semibold text-gray-800\",\n                                                        children: (profile === null || profile === void 0 ? void 0 : profile.tenth_year_of_passing) ? \"\".concat(parseInt(profile.tenth_year_of_passing) - 1, \" - \").concat(profile.tenth_year_of_passing) : '-'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 515,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 506,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start mb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-6 w-full\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-500 text-sm w-[120px]\",\n                                                                            children: \"School :\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 526,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-700 font-medium\",\n                                                                            children: (profile === null || profile === void 0 ? void 0 : profile.tenth_school) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 527,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                    lineNumber: 525,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-500 text-sm w-[120px]\",\n                                                                            children: \"Board :\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 530,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-700 font-medium\",\n                                                                            children: (profile === null || profile === void 0 ? void 0 : profile.tenth_board) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 531,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                    lineNumber: 529,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 524,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-500 text-sm w-[120px]\",\n                                                                            children: \"Location :\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 536,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-700 font-medium\",\n                                                                            children: (profile === null || profile === void 0 ? void 0 : profile.city) || '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 537,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                    lineNumber: 535,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-500 text-sm w-[120px]\",\n                                                                            children: \"Specialization :\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 540,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-700 font-medium\",\n                                                                            children: \"-\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                            lineNumber: 541,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                                    lineNumber: 539,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 534,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 523,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 522,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 505,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                lineNumber: 413,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg p-5 shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-4 text-gray-800\",\n                                        children: \"Companies\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 551,\n                                        columnNumber: 13\n                                    }, this),\n                                    companyStats.loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center py-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBuilding_FaFileAlt_FaMapMarkerAlt_FaPhoneAlt_FaSpinner_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_6__.FaSpinner, {\n                                                className: \"animate-spin text-blue-500 text-xl mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 555,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Loading company data...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 556,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 554,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-500 text-sm\",\n                                                            children: \"Total Listings\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 563,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-semibold text-gray-700\",\n                                                            children: companyStats.totalListings\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 564,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 562,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 561,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-500 text-sm\",\n                                                            children: \"Eligible Jobs\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 571,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-semibold text-gray-700\",\n                                                            children: companyStats.eligibleJobs\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 572,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 570,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 569,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                lineNumber: 550,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                        lineNumber: 411,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-3 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg p-5 shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-4 text-gray-800\",\n                                        children: \"My Files\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 584,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center p-2 mb-3 cursor-pointer hover:bg-gray-50 rounded-lg transition-colors\",\n                                        onClick: ()=>setIsResumeModalOpen(true),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-blue-600\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"h-6 w-6\",\n                                                        fill: \"none\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        stroke: \"currentColor\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 593,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 592,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 591,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 590,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-grow\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-medium text-gray-700\",\n                                                        children: \"Resumes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 598,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: resumeCount > 0 ? \"\".concat(resumeCount, \" resume\").concat(resumeCount > 1 ? 's' : '', \" uploaded\") + (lastResumeUpdate ? \" • Last updated \".concat(new Date(lastResumeUpdate).toLocaleDateString()) : '') : 'No resumes uploaded'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 599,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 597,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-50 px-3 py-1 rounded-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-600 font-medium\",\n                                                    children: resumeCount\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 608,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 607,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 586,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center p-2 cursor-pointer hover:bg-gray-50 rounded-lg transition-colors\",\n                                        onClick: ()=>setIsDocumentsModalOpen(true),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-blue-600\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"h-6 w-6\",\n                                                        fill: \"none\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        stroke: \"currentColor\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                            lineNumber: 619,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 618,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 617,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 616,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-grow\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-medium text-gray-700\",\n                                                        children: \"Documents\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 624,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Academic certificates and marksheets\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 625,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 623,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-50 px-3 py-1 rounded-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-600 font-medium\",\n                                                    children: ((profile === null || profile === void 0 ? void 0 : profile.tenth_certificate) ? 1 : 0) + ((profile === null || profile === void 0 ? void 0 : profile.twelfth_certificate) ? 1 : 0) + semesterMarksheets.length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                    lineNumber: 628,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 627,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 612,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                lineNumber: 583,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg p-5 shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold text-gray-800\",\n                                            children: \"CURRENT ADDRESS\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                            lineNumber: 640,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 639,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500 w-20\",\n                                                        children: \"City\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 645,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-700\",\n                                                        children: [\n                                                            \": \",\n                                                            (profile === null || profile === void 0 ? void 0 : profile.city) || '-'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 646,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 644,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500 w-20\",\n                                                        children: \"District\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 649,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-700\",\n                                                        children: [\n                                                            \": \",\n                                                            (profile === null || profile === void 0 ? void 0 : profile.district) || '-'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 650,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 648,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500 w-20\",\n                                                        children: \"State\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 653,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-700\",\n                                                        children: [\n                                                            \": \",\n                                                            (profile === null || profile === void 0 ? void 0 : profile.state) || '-'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 654,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 652,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500 w-20\",\n                                                        children: \"Pin Code\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 657,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-700\",\n                                                        children: [\n                                                            \": \",\n                                                            (profile === null || profile === void 0 ? void 0 : profile.pincode) || '-'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 658,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 656,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500 w-20\",\n                                                        children: \"Country\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 661,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-700\",\n                                                        children: [\n                                                            \": \",\n                                                            (profile === null || profile === void 0 ? void 0 : profile.country) || '-'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 662,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 660,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500 w-20\",\n                                                        children: \"Address\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 665,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-700\",\n                                                        children: [\n                                                            \": \",\n                                                            (profile === null || profile === void 0 ? void 0 : profile.address) || '-'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                        lineNumber: 666,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                                lineNumber: 664,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                        lineNumber: 643,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                                lineNumber: 638,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                        lineNumber: 581,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ResumeModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        isOpen: isResumeModalOpen,\n                        onClose: ()=>setIsResumeModalOpen(false),\n                        resume: (profile === null || profile === void 0 ? void 0 : profile.resume_url) || (profile === null || profile === void 0 ? void 0 : profile.resume),\n                        onUpload: handleResumeUpload,\n                        onDelete: handleResumeDelete\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                        lineNumber: 674,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DocumentsModal__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        isOpen: isDocumentsModalOpen,\n                        onClose: ()=>setIsDocumentsModalOpen(false),\n                        documents: {\n                            tenth: (profile === null || profile === void 0 ? void 0 : profile.tenth_certificate_url) || (profile === null || profile === void 0 ? void 0 : profile.tenth_certificate),\n                            twelfth: (profile === null || profile === void 0 ? void 0 : profile.twelfth_certificate_url) || (profile === null || profile === void 0 ? void 0 : profile.twelfth_certificate),\n                            semesterMarksheets: semesterMarksheets\n                        },\n                        onUploadCertificate: _api_students__WEBPACK_IMPORTED_MODULE_3__.studentsAPI.uploadCertificate,\n                        onUploadMarksheet: _api_students__WEBPACK_IMPORTED_MODULE_3__.studentsAPI.uploadSemesterMarksheet\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                        lineNumber: 683,\n                        columnNumber: 7\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n                lineNumber: 194,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\StudentProfile.jsx\",\n        lineNumber: 146,\n        columnNumber: 5\n    }, this);\n}\n_s(StudentProfile, \"MlOUd1h8jr6IPmmk+kBfN+tm6uo=\");\n_c = StudentProfile;\nvar _c;\n$RefreshReg$(_c, \"StudentProfile\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/student-management/StudentProfile.jsx\n"));

/***/ })

});